# Grafana Alloy Log Collection Configuration

This configuration sets up <PERSON><PERSON> Alloy to collect logs from two sources:
1. **Local system log files** from the server filesystem
2. **AWS CloudWatch logs** from Lambda functions

## Overview

The pipeline collects, processes, and forwards logs to a local Loki instance for centralized log management and analysis.

## Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────┐
│ Local Log Files │───▶│              │    │             │
└─────────────────┘    │              │    │             │
                       │ Grafana Alloy│───▶│    Loki     │
┌─────────────────┐    │              │    │             │
│ AWS CloudWatch  │───▶│              │    │             │
└─────────────────┘    └──────────────┘    └─────────────┘
```

## Configuration Components

### 1. Local File Collection

Monitors system log files from `/temp/logs/` directory:

- **syslog** - System messages
- **alternatives.log** - Alternative system configurations
- **dmesg** - Kernel ring buffer messages
- **dpkg.log** - Package management logs
- **kern.log** - Kernel logs
- **lastlog** - User login records

Each file is tagged with:
- `job` - Identifies the log type
- `hostname` - Server identifier (`log-server-1`)

### 2. AWS CloudWatch Integration

#### Configuration Details

```hcl
otelcol.receiver.awscloudwatch "default" {
  region = "us-east-1"

  logs {
    poll_interval = "1m"
    max_events_per_request = 5000

    groups {
      named {
        group_name = "/aws/lambda/Dramcash"
        names = [
          # Specific log streams for the Dramcash Lambda function
        ]
      }
    }
  }
}
```

**Key Parameters:**
- **Region**: `us-east-1` - AWS region where CloudWatch logs reside
- **Poll Interval**: `1m` - Checks for new logs every minute
- **Max Events**: `5000` - Maximum log events per API request
- **Log Group**: `/aws/lambda/Dramcash` - Specific Lambda function logs
- **Log Streams**: 12 specific stream names from December 10, 2024

#### Processing Pipeline

The CloudWatch logs go through several processing stages:

1. **Batch Processing** - Groups logs for efficient processing
2. **Attribute Processing** - Adds AWS metadata as Loki labels
3. **Debug Export** - Outputs detailed logs for troubleshooting
4. **Loki Processing** - Extracts and transforms log content

### 3. Log Processing (`loki.process "extract_body"`)

This is the core component that transforms CloudWatch logs:

#### Stage 1: Label Management
```hcl
stage.label_drop {
  values = ["exporter", "detected_level", "cloudwatch_log_group_name"]
}
```
Removes unwanted labels to keep the label set clean.

#### Stage 2: Label Mapping
```hcl
stage.labels {
  values = {
    filename = "cloudwatch_log_group_name",
  }
}
```
Maps CloudWatch log group name to a `filename` label.

#### Stage 3: Static Labels
```hcl
stage.static_labels {
  values = {
    job            = "aws-logs",
    hostname       = "cloud-server-1",
    detected_level = "unknown",
  }
}
```
Adds consistent labels to all CloudWatch logs for identification and filtering.

#### Stage 4: JSON Parsing
```hcl
stage.json {
  expressions = {
    body = "body",
  }
}
```
Extracts the `body` field from CloudWatch JSON log entries.

**Input Example:**
```json
{
  "body": "[ERROR] Runtime.ImportModuleError: Unable to import module 'lambda_function': No module named 'bson'",
  "attributes": {"id": "38665755374436029209234514929969842467579952628660371457"},
  "resources": {
    "cloudwatch.log.group.name": "/aws/lambda/Dramcash",
    "cloudwatch.log.stream": "2024/12/10/[$LATEST]5a5e5473c1274f5ebbd73db7144e2882"
  }
}
```

#### Stage 5: Output
```hcl
stage.output {
  source = "body"
}
```
Sets the final log line to contain only the extracted body content.

**Output:**
```
[ERROR] Runtime.ImportModuleError: Unable to import module 'lambda_function': No module named 'bson'
```

## Loki Integration

All processed logs are sent to a local Loki instance at `http://loki:3100/loki/api/v1/push`.

### Label Structure

**Local System Logs:**
- `job`: Log type (e.g., `system-logs`, `kern-logs`)
- `hostname`: `log-server-1`
- `__path__`: Original file path

**AWS CloudWatch Logs:**
- `job`: `aws-logs`
- `hostname`: `cloud-server-1`
- `detected_level`: `unknown`
- `filename`: CloudWatch log group name
- `aws.region`: AWS region
- `cloudwatch.log.group.name`: Original log group

## Querying Examples

### Loki Query Examples

```promql
# All AWS logs
{job="aws-logs"}

# AWS errors only
{job="aws-logs"} |= "ERROR"

# System kernel logs
{job="kern-logs"}

# All logs from cloud server
{hostname="cloud-server-1"}

# Lambda function errors
{job="aws-logs"} |= "Runtime.ImportModuleError"
```

## Monitoring and Debugging

### Debug Output
The configuration includes detailed debug logging:
```hcl
otelcol.exporter.debug "default" {
  verbosity           = "detailed"
  sampling_initial    = 2
  sampling_thereafter = 1
  use_internal_logger = true
}
```

### Live Debugging
```hcl
livedebugging {
  enabled = true
}
```

## Prerequisites

1. **AWS Credentials** - Alloy needs permissions to read CloudWatch logs
2. **Loki Instance** - Running at `http://loki:3100`
3. **File Permissions** - Read access to log files in `/temp/logs/`


#### Docker Compose for Local Testing

```yaml
version: '3.8'
services:
  alloy:
    image: grafana/alloy:latest
    environment:
      - AWS_ACCESS_KEY_ID=your_access_key_here
      - AWS_SECRET_ACCESS_KEY=your_secret_key_here
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_SESSION_TOKEN=your_session_token_here
    volumes:
      - ./alloy-config.alloy:/etc/alloy/config.alloy
    ports:
      - "12345:12345"
    command:
      - run
      - /etc/alloy/config.alloy
      - --server.http.listen-addr=0.0.0.0:12345

  loki:
    image: grafana/loki:latest
    ports:
      - "3100:3100"
    command: -config.file=/etc/loki/local-config.yaml
```

This allows you to:
- Test CloudWatch log ingestion without EC2 system logs
- Run Alloy locally with just AWS credentials
- Include Loki service for complete local testing setup

## AWS IAM Permissions

Required IAM permissions for CloudWatch log access:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:DescribeLogGroups",
        "logs:DescribeLogStreams",
        "logs:GetLogEvents"
      ],
      "Resource": "arn:aws:logs:us-east-1:*:log-group:/aws/lambda/Dramcash:*"
    }
  ]
}
```
