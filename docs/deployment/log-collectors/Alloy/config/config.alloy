livedebugging {
  enabled = true
}

local.file_match "system_logs" {
  path_targets = [{
      "__path__" = "/temp/logs/syslog",
      "job"       = "system-logs",
      "hostname"  = "log-server-1", },
      {
      "__path__" = "/temp/logs/alternatives.log",
      "job"       = "alternatives-logs",
      "hostname"  = "log-server-1",
    },
    {
      "__path__" = "/temp/logs/dmesg",
      "job"       = "dmesg-logs",
      "hostname"  = "log-server-1",
    },
     {
      "__path__" = "/temp/logs/dpkg.log",
      "job"       = "dpkg-logs",
      "hostname"  = "log-server-1",
    },
     {
      "__path__" = "/temp/logs/kern.log",
      "job"       = "kern-logs",
      "hostname"  = "log-server-1",
    },
    {
      "__path__" = "/temp/logs/lastlog",
      "job"       = "last-logs",
      "hostname"  = "log-server-1",
    }
  ]
  sync_period = "5s"
}


loki.source.file "system_logs_source" {
  targets    = local.file_match.system_logs.targets
  forward_to = [loki.write.local.receiver]
}

otelcol.receiver.awscloudwatch "default" {
  region = "us-east-1"

  logs {
    poll_interval = "1m"
    max_events_per_request = 5000

    groups {
      named {
        group_name = "/aws/lambda/Dramcash"
        names = [
          "2024/12/10/[$LATEST]5a5e5473c1274f5ebbd73db7144e2882",
          "2024/12/10/[$LATEST]b49b47c2a9ea4fdda8ad839c713ad7ae",
          "2024/12/10/[$LATEST]5da6b6f46bd34539ad4e96091b9a43f6",
          "2024/12/10/[$LATEST]a7e5f118a54b46429fa9811e9372945e",
          "2024/12/10/[$LATEST]2bc391b255044981afca0e0f6dc733fa",
          "2024/12/10/[$LATEST]5326f3ce442d419e9a2b5f0573e7d098",
          "2024/12/10/[$LATEST]ecda7406e85b4cf5a3f74b641ea13a9e",
          "2024/12/10/[$LATEST]200096e2f6564cbeb85de6fe7e17c48c",
          "2024/12/10/[$LATEST]d61d2dc0dcb94a06af366cd3a6d0b00f",
          "2024/12/10/[$LATEST]b2b58205e43d477ebed293976e16f0aa",
          "2024/12/10/[$LATEST]6506f30db51c409297807f2349ae3186",
          "2024/12/10/[$LATEST]c1f64324ef87421fa285e4126c0e6906",
        ]
      }
    }
  }

  output {
    logs = [otelcol.processor.batch.default.input]
  }
}

otelcol.processor.batch "default" {
  output {
    logs = [otelcol.processor.attributes.default.input]
  }
}

otelcol.processor.attributes "default" {
  action {
    key    = "loki.resource.labels"
    action = "insert"
    value  = "aws.region,cloudwatch.log.group.name"
  }

  output {
    logs = [
      otelcol.exporter.loki.default.input,
      otelcol.exporter.debug.default.input,
    ]
  }
}

otelcol.exporter.debug "default" {
  verbosity           = "detailed"
  sampling_initial    = 2
  sampling_thereafter = 1
  use_internal_logger = true
}

otelcol.exporter.loki "default" {
  forward_to = [loki.process.extract_body.receiver]
}

loki.process "extract_body" {
  forward_to = [loki.write.local.receiver]

  stage.label_drop {
    values = ["exporter", "detected_level", "cloudwatch_log_group_name"]
  }

  stage.labels {
    values = {
      filename = "cloudwatch_log_group_name",
    }
  }

  stage.static_labels {
    values = {
      job            = "aws-logs",
      hostname       = "cloud-server-1",
      detected_level = "unknown",
    }
  }

  stage.json {
    expressions = {
      body = "body",
    }
  }

  stage.output {
    source = "body"
  }
}

loki.write "local" {
  endpoint {
    url = "http://loki:3100/loki/api/v1/push"
  }
}
