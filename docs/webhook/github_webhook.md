# GitHub Webhook Usage Guide

## 📌 What is a Webhook?

A **webhook** is a way for GitHub to notify your application when certain events happen in a repository, such as a push, pull request, issue creation, etc. Your application can then respond accordingly, like triggering CI/CD pipelines, updating a dashboard, or sending alerts.

---

## ✅ How to Add a Webhook in GitHub

1. Go to your **GitHub repository**.
2. Click on the **Settings** tab.
3. In the left sidebar, click **Webhooks**.
4. Click the **"Add webhook"** button.
5. Fill out the webhook details:

   * **Payload URL**: Your server endpoint (e.g., `https://your-domain.com/webhooks/github_webhook`)
   * **Content type**: `application/json`
   * **Secret**: Optional but recommended for securing webhook
   * **Events to trigger webhook**:

     * Choose "Just the push event" or "Let me select individual events"
   * Click **Add webhook**

---

## 🧪 Sample Webhook Payload (Push Event)

```json
{
  "action": "opened",
  "issue": {
    "url": "https://api.github.com/repos/anand176/AnomalyNarration/issues/1",
    "repository_url": "https://api.github.com/repos/anand176/AnomalyNarration",
    "labels_url": "https://api.github.com/repos/anand176/AnomalyNarration/issues/1/labels{/name}",
    "comments_url": "https://api.github.com/repos/anand176/AnomalyNarration/issues/1/comments",
    "events_url": "https://api.github.com/repos/anand176/AnomalyNarration/issues/1/events",
    "html_url": "https://github.com/anand176/AnomalyNarration/issues/1",
    "id": 3292531563,
    "node_id": "I_kwDOMaeKjs7EQAtr",
    "number": 1,
    "title": "creating  a issue",
    "user": {
      "login": "anand176",
      "id": 75465645,
      "node_id": "MDQ6VXNlcjc1NDY1NjQ1",
      "avatar_url": "https://avatars.githubusercontent.com/u/75465645?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/anand176",
      "html_url": "https://github.com/anand176",
      "followers_url": "https://api.github.com/users/anand176/followers",
      "following_url": "https://api.github.com/users/anand176/following{/other_user}",
      "gists_url": "https://api.github.com/users/anand176/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/anand176/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/anand176/subscriptions",
      "organizations_url": "https://api.github.com/users/anand176/orgs",
      "repos_url": "https://api.github.com/users/anand176/repos",
      "events_url": "https://api.github.com/users/anand176/events{/privacy}",
      "received_events_url": "https://api.github.com/users/anand176/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false
    },
    "labels": [

    ],
    "state": "open",
    "locked": false,
    "assignee": null,
    "assignees": [

    ],
    "milestone": null,
    "comments": 0,
    "created_at": "2025-08-05T10:28:16Z",
    "updated_at": "2025-08-05T10:28:16Z",
    "closed_at": null,
    "author_association": "OWNER",
    "active_lock_reason": null,
    "sub_issues_summary": {
      "total": 0,
      "completed": 0,
      "percent_completed": 0
    },
    "body": "Issue is created",
    "reactions": {
      "url": "https://api.github.com/repos/anand176/AnomalyNarration/issues/1/reactions",
      "total_count": 0,
      "+1": 0,
      "-1": 0,
      "laugh": 0,
      "hooray": 0,
      "confused": 0,
      "heart": 0,
      "rocket": 0,
      "eyes": 0
    },
    "timeline_url": "https://api.github.com/repos/anand176/AnomalyNarration/issues/1/timeline",
    "performed_via_github_app": null,
    "state_reason": null
  },
  "repository": {
    "id": 833063566,
    "node_id": "R_kgDOMaeKjg",
    "name": "AnomalyNarration",
    "full_name": "anand176/AnomalyNarration",
    "private": false,
    "owner": {
      "login": "anand176",
      "id": 75465645,
      "node_id": "MDQ6VXNlcjc1NDY1NjQ1",
      "avatar_url": "https://avatars.githubusercontent.com/u/75465645?v=4",
      "gravatar_id": "",
      "url": "https://api.github.com/users/anand176",
      "html_url": "https://github.com/anand176",
      "followers_url": "https://api.github.com/users/anand176/followers",
      "following_url": "https://api.github.com/users/anand176/following{/other_user}",
      "gists_url": "https://api.github.com/users/anand176/gists{/gist_id}",
      "starred_url": "https://api.github.com/users/anand176/starred{/owner}{/repo}",
      "subscriptions_url": "https://api.github.com/users/anand176/subscriptions",
      "organizations_url": "https://api.github.com/users/anand176/orgs",
      "repos_url": "https://api.github.com/users/anand176/repos",
      "events_url": "https://api.github.com/users/anand176/events{/privacy}",
      "received_events_url": "https://api.github.com/users/anand176/received_events",
      "type": "User",
      "user_view_type": "public",
      "site_admin": false
    },
    "html_url": "https://github.com/anand176/AnomalyNarration",
    "description": null,
    "fork": false,
    "url": "https://api.github.com/repos/anand176/AnomalyNarration",
    "forks_url": "https://api.github.com/repos/anand176/AnomalyNarration/forks",
    "keys_url": "https://api.github.com/repos/anand176/AnomalyNarration/keys{/key_id}",
    "collaborators_url": "https://api.github.com/repos/anand176/AnomalyNarration/collaborators{/collaborator}",
    "teams_url": "https://api.github.com/repos/anand176/AnomalyNarration/teams",
    "hooks_url": "https://api.github.com/repos/anand176/AnomalyNarration/hooks",
    "issue_events_url": "https://api.github.com/repos/anand176/AnomalyNarration/issues/events{/number}",
    "events_url": "https://api.github.com/repos/anand176/AnomalyNarration/events",
    "assignees_url": "https://api.github.com/repos/anand176/AnomalyNarration/assignees{/user}",
    "branches_url": "https://api.github.com/repos/anand176/AnomalyNarration/branches{/branch}",
    "tags_url": "https://api.github.com/repos/anand176/AnomalyNarration/tags",
    "blobs_url": "https://api.github.com/repos/anand176/AnomalyNarration/git/blobs{/sha}",
    "git_tags_url": "https://api.github.com/repos/anand176/AnomalyNarration/git/tags{/sha}",
    "git_refs_url": "https://api.github.com/repos/anand176/AnomalyNarration/git/refs{/sha}",
    "trees_url": "https://api.github.com/repos/anand176/AnomalyNarration/git/trees{/sha}",
    "statuses_url": "https://api.github.com/repos/anand176/AnomalyNarration/statuses/{sha}",
    "languages_url": "https://api.github.com/repos/anand176/AnomalyNarration/languages",
    "stargazers_url": "https://api.github.com/repos/anand176/AnomalyNarration/stargazers",
    "contributors_url": "https://api.github.com/repos/anand176/AnomalyNarration/contributors",
    "subscribers_url": "https://api.github.com/repos/anand176/AnomalyNarration/subscribers",
    "subscription_url": "https://api.github.com/repos/anand176/AnomalyNarration/subscription",
    "commits_url": "https://api.github.com/repos/anand176/AnomalyNarration/commits{/sha}",
    "git_commits_url": "https://api.github.com/repos/anand176/AnomalyNarration/git/commits{/sha}",
    "comments_url": "https://api.github.com/repos/anand176/AnomalyNarration/comments{/number}",
    "issue_comment_url": "https://api.github.com/repos/anand176/AnomalyNarration/issues/comments{/number}",
    "contents_url": "https://api.github.com/repos/anand176/AnomalyNarration/contents/{+path}",
    "compare_url": "https://api.github.com/repos/anand176/AnomalyNarration/compare/{base}...{head}",
    "merges_url": "https://api.github.com/repos/anand176/AnomalyNarration/merges",
    "archive_url": "https://api.github.com/repos/anand176/AnomalyNarration/{archive_format}{/ref}",
    "downloads_url": "https://api.github.com/repos/anand176/AnomalyNarration/downloads",
    "issues_url": "https://api.github.com/repos/anand176/AnomalyNarration/issues{/number}",
    "pulls_url": "https://api.github.com/repos/anand176/AnomalyNarration/pulls{/number}",
    "milestones_url": "https://api.github.com/repos/anand176/AnomalyNarration/milestones{/number}",
    "notifications_url": "https://api.github.com/repos/anand176/AnomalyNarration/notifications{?since,all,participating}",
    "labels_url": "https://api.github.com/repos/anand176/AnomalyNarration/labels{/name}",
    "releases_url": "https://api.github.com/repos/anand176/AnomalyNarration/releases{/id}",
    "deployments_url": "https://api.github.com/repos/anand176/AnomalyNarration/deployments",
    "created_at": "2024-07-24T09:28:02Z",
    "updated_at": "2024-09-30T10:13:27Z",
    "pushed_at": "2024-09-30T10:13:24Z",
    "git_url": "git://github.com/anand176/AnomalyNarration.git",
    "ssh_url": "**************:anand176/AnomalyNarration.git",
    "clone_url": "https://github.com/anand176/AnomalyNarration.git",
    "svn_url": "https://github.com/anand176/AnomalyNarration",
    "homepage": null,
    "size": 22075,
    "stargazers_count": 0,
    "watchers_count": 0,
    "language": "Python",
    "has_issues": true,
    "has_projects": true,
    "has_downloads": true,
    "has_wiki": true,
    "has_pages": false,
    "has_discussions": false,
    "forks_count": 0,
    "mirror_url": null,
    "archived": false,
    "disabled": false,
    "open_issues_count": 1,
    "license": null,
    "allow_forking": true,
    "is_template": false,
    "web_commit_signoff_required": false,
    "topics": [

    ],
    "visibility": "public",
    "forks": 0,
    "open_issues": 1,
    "watchers": 0,
    "default_branch": "master"
  },
  "sender": {
    "login": "anand176",
    "id": 75465645,
    "node_id": "MDQ6VXNlcjc1NDY1NjQ1",
    "avatar_url": "https://avatars.githubusercontent.com/u/75465645?v=4",
    "gravatar_id": "",
    "url": "https://api.github.com/users/anand176",
    "html_url": "https://github.com/anand176",
    "followers_url": "https://api.github.com/users/anand176/followers",
    "following_url": "https://api.github.com/users/anand176/following{/other_user}",
    "gists_url": "https://api.github.com/users/anand176/gists{/gist_id}",
    "starred_url": "https://api.github.com/users/anand176/starred{/owner}{/repo}",
    "subscriptions_url": "https://api.github.com/users/anand176/subscriptions",
    "organizations_url": "https://api.github.com/users/anand176/orgs",
    "repos_url": "https://api.github.com/users/anand176/repos",
    "events_url": "https://api.github.com/users/anand176/events{/privacy}",
    "received_events_url": "https://api.github.com/users/anand176/received_events",
    "type": "User",
    "user_view_type": "public",
    "site_admin": false
  }
}
```

---

## 🔐 Required Permissions

To **add or view webhooks** in a repository, you need to have at least:

* **Admin** access to the repository.

If using GitHub Apps or OAuth Apps:

* Ensure the app has the **`webhooks`** scope/permission.
* Also grant access to specific events you care about (e.g., `push`, `issues`, `pull_request`).

---

## 📦 Sample Template Webhook Payload (Issue Event)

```json
{
  "action": "opened",
  "issue": {
    "number": 1,
    "title": "creating a issue",
    "body": "Issue is created",
    "state": "open",
    "user": {
      "login": "your-username",
      "id": 123456,
      "html_url": "https://github.com/your-username"
    },
    "repository_url": "https://api.github.com/repos/your-username/your-repo",
    "html_url" "https://github.com/your-username/your-repo/issues/1"
  },
  "repository": {
    "id": 123456789,
    "name": "your-repo",
    "full_name": "your-username/your-repo",
    "html_url": "https://github.com/your-username/your-repo"
  },
  "sender": {
    "login": "your-username",
    "id": 123456,
    "html_url": "https://github.com/your-username"
  }
}
```

---
