# Frontend Application

The frontend is a modern React application built with TypeScript, providing a comprehensive user interface for the incident management system.

## Technology Stack

### Core Technologies

- **React** - Modern React with latest features
- **TypeScript** - Type-safe JavaScript development
- **Vite** - Fast build tool and development server
- **React Router** - Client-side routing

### UI Framework & Styling

- **Mantine** - Comprehensive React components library
- **Tailwind CSS** - Utility-first CSS framework (via Vite plugin)
- **Lucide React** - Beautiful icon library

### State Management & Data Fetching

- **TanStack React Query** - Server state management
- **React Hook Form** - Form handling and validation
- **Zod** - Schema validation

### Additional Libraries

- **React Markdown** - Markdown rendering
- **Recharts** - Chart components
- **Day.js** - Date manipulation
- **React Toastify** - Toast notifications

## Application Structure

### Main Application (`App.tsx`)

The main application component sets up:

- Browser routing with React Router
- Authentication context
- Protected routes
- Lazy loading for performance
- Global layout structure

### Key Pages

- **Dashboard** - Overview of incidents, metrics, and charts
- **Incidents** - List and manage incidents
- **IncidentDetail** - Detailed incident view with timeline
- **CreateIncident** - Form to create new incidents
- **Users** - User management
- **Logs** - Log viewing and analysis
- **Traces** - Distributed tracing
- **Metrics** - System metrics and monitoring
- **KnowledgeBase** - Documentation and knowledge articles

### Core Components

#### Layout Components

- **Layout** - Main application layout wrapper
- **Sidebar** - Navigation sidebar with project structure
- **Navbar** - Top navigation bar
- **ProtectedRoute** - Route protection for authenticated users

#### Incident Components

- **IncidentDetailLayout** - Reusable layout for incident pages
- **IncidentHeader** - Incident information header
- **IncidentTimelineView** - Timeline visualization

## Authentication & Security

### Authentication Context

The application uses a React context for authentication state management:

- User login/logout functionality
- Token-based authentication
- Protected route handling
- Automatic token refresh

### Route Protection

All main application routes are protected and require authentication:

```typescript
const protectedRoutes = [
  { path: "/dashboard", element: <Dashboard /> },
  { path: "/incidents", element: <Incidents /> },
  { path: "/incident/:incidentId", element: <IncidentDetail /> },
  // ... other routes
];
```

## Development Setup

### Prerequisites

- Node.js 18+
- pnpm (preferred package manager)

### Installation

```bash
cd frontend
pnpm install
```

### Development Scripts

```bash
# Start development server
pnpm dev

# Build for production
pnpm build

# Run tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Lint code
pnpm lint

# Format code
pnpm format
```

### Environment Configuration

The application supports runtime configuration through a `config.js` file:

```javascript
window.CONFIG = {
  API_URL: "http://localhost:2000/api",
};
```

## Build & Deployment

### Docker Build

The application uses a multi-stage Docker build:

1. **Build Stage**: Uses Node.js 22 Alpine to build the React application
2. **Serve Stage**: Uses Nginx Alpine to serve the built application

### Configuration

- **Development**: API URL defaults to `http://localhost:2000/api`
- **Production**: Configurable via environment variables
- **Port**: Exposed on port 2000

### Nginx Configuration

Custom Nginx configuration handles:

- Single Page Application routing
- API proxy configuration
- Static file serving
- Health checks

## Key Features

### Responsive Design

- Mobile-first approach with Tailwind CSS
- Responsive grid layouts
- Adaptive navigation
- Touch-friendly interfaces

### Performance Optimizations

- Lazy loading of route components
- React Query for efficient data fetching
- Memoization of expensive components
- Code splitting and bundling optimization

### User Experience

- Loading states and error handling
- Toast notifications for user feedback
- Consistent design system with Mantine
- Accessible components and navigation

### Data Visualization

- Interactive charts with Recharts
- Real-time metrics display
- Timeline visualizations
- Dashboard analytics

## State Management

### Server State

React Query handles all server state management:

- Automatic caching and synchronization
- Background refetching
- Optimistic updates
- Error handling and retries

### Form State

React Hook Form manages form state:

- Validation with Zod schemas
- Performance optimization
- Error handling
- Controlled and uncontrolled inputs

### Global State

Context API for application-wide state:

- Authentication state
- User preferences
- Theme configuration

## Testing

### Test Setup

- **Jest** - Testing framework
- **React Testing Library** - Component testing utilities
- **TypeScript** - Type checking in tests

### Test Scripts

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch
```

## Code Quality

### Linting & Formatting

- **ESLint** - Code linting with TypeScript support
- **Prettier** - Code formatting
- **Husky** - Git hooks for quality checks

### TypeScript Configuration

- Strict type checking enabled
- Modern ES2020 target
- React JSX support
- Path mapping for imports

## API Integration

### Base Configuration

The frontend communicates with the backend API through:

- Configurable API base URL
- Automatic request/response handling
- Error handling and retry logic
- Authentication token management

### Data Fetching Patterns

- React Query hooks for data fetching
- Optimistic updates for better UX
- Background synchronization
- Cache invalidation strategies

## Browser Support

### Target Browsers

- Modern browsers supporting ES2020
- Chrome 80+
- Firefox 72+
- Safari 13+
- Edge 80+

### Progressive Enhancement

- Core functionality works without JavaScript
- Enhanced experience with full JavaScript support
- Responsive design for all screen sizes
