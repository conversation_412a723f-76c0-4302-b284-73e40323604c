# Qdrant Vector DB Integration Architecture for Incident Similarity Search

## Overview

This document describes the architecture for integrating Qdrant as a vector database to enable semantic similarity search over incident details. The system provides:

- Storage of incident details as vectorized documents with incident IDs as metadata
- Automatic embedding generation for new and updated incidents via async tasks
- Manual similarity search capabilities using flexible text input
- Non-blocking operations that maintain core incident management functionality

## High-Level Architecture

```
incidentmanagement/backend/app/
  vector_db/
    __init__.py
    qdrant_connector.py      # Qdrant client, upsert, search, and schema management
    embeddings.py            # Text embedding logic using Gemini via LiteLLM
  tasks/
    vector_db.py             # Async Celery tasks for embedding operations
  routes/
    data_bank/               # Vector DB API endpoints
      __init__.py
      controller.py          # API endpoints for vector DB operations
      service.py             # Business logic for vector DB operations
      models.py              # Pydantic models for request/response validation
    incidents/
      service.py             # Enhanced with async embedding task triggers
  utils/
    celery_worker.py         # Celery configuration and task registration
```

## System Architecture Diagram

```mermaid
flowchart TD
    A[Incident Created/Updated] -->|Trigger Async Task| B[Celery Task Queue]
    B -->|Process Task| C[Embedding Generation]
    C -->|Gemini API| D[768-dim Vector]
    D -->|Upsert| E[Qdrant Vector DB]

    F[Manual Search Request] -->|Text Input| G[Embedding Generation]
    G -->|Gemini API| H[Query Vector]
    H -->|Search| E
    E -->|Similar Incidents| I[Search Results]

    subgraph "Non-blocking Architecture"
        A
        J[Incident Operation Complete]
        A -.->|Immediate Response| J
    end

    subgraph "Background Processing"
        B
        C
        D
        E
    end
```

## Key Components

### Vector Database Layer

- **vector_db/qdrant_connector.py**: Abstraction for Qdrant operations with 768-dimensional vector support

  - Collection management and initialization
  - Vector upsert with metadata storage
  - Similarity search with configurable top-k results
  - Comprehensive error handling and logging

- **vector_db/embeddings.py**: Text-to-vector conversion using Google Gemini
  - `embed_incident()`: Converts incident data to embeddings
  - `embed_text()`: Converts arbitrary text to embeddings
  - Uses LiteLLM for API abstraction (no local ML dependencies)
  - Returns 768-dimensional vectors

### Async Task Layer

- **tasks/vector_db.py**: Celery tasks for background embedding operations
  - `upsert_incident_embedding_task`: Async incident embedding creation
  - `update_incident_embedding_task`: Async incident embedding updates
  - Retry logic with exponential backoff (3 retries: 60s→120s→240s)
  - Task status tracking and monitoring

### API Layer

- **routes/data_bank/controller.py**: RESTful endpoints for vector operations

  - Synchronous embedding operations
  - Async task triggering with task ID response
  - Task status monitoring
  - Manual similarity search

- **routes/data_bank/service.py**: Business logic orchestration

  - Embedding generation coordination
  - Vector DB operation management
  - Error handling and logging

- **routes/data_bank/models.py**: Request/response validation
  - `IncidentData`: Structured incident data model
  - `TextSearchRequest`: Flexible text search input
  - `SimilaritySearchResponse`: Search results with scores

### Integration Layer

- **routes/incidents/service.py**: Enhanced incident operations
  - Automatic async embedding task triggering on create/update
  - Non-blocking operations with graceful degradation
  - Comprehensive logging for monitoring

## API Endpoints

### Data Bank Operations

- **POST /data_bank/upsert-incident**: Direct synchronous embedding upsert
- **POST /data_bank/update-embedding**: Direct synchronous embedding update
- **POST /data_bank/similarity-search**: Manual text-based similarity search
- **POST /data_bank/trigger-embedding-job**: Manual async embedding job trigger
- **GET /data_bank/task-status/{task_id}**: Async task status monitoring

### Enhanced Incident Operations

- **POST /incidents**: Creates incident + triggers async embedding task
- **PUT /incidents/{id}**: Updates incident + triggers async embedding update

## Embedding Strategy

### Automatic Async Operations

```
Incident Create/Update → Celery Task Queue → Background Embedding → Qdrant Upsert
```

- Non-blocking: Main operations complete immediately
- Resilient: Vector DB failures don't impact core functionality
- Scalable: Background processing handles load spikes

### Manual Similarity Search

```
User Request → Immediate Embedding → Qdrant Search → Results
```

- On-demand: Only triggered by explicit API calls
- Flexible: Accepts any text input (titles, descriptions, logs, errors)
- Configurable: User-specified result count (1-50)

## Data Models

### Vector Storage

- **Vector Dimensions**: 768 (Google Gemini text-embedding-004)
- **Metadata**: Incident ID and minimal reference data
- **Collection**: Configurable name (default: "incidents")

### Embedding Content

- **Incident Data**: Title + description concatenation
- **Arbitrary Text**: Direct text input for search
- **Preprocessing**: Handles empty fields and edge cases

## Configuration

### Environment Variables

```env
# Vector Database
QDRANT_URL=http://qdrant:6333
QDRANT_COLLECTION=incidents
QDRANT_API_KEY=optional-for-cloud

# Embedding API
GEMINI_API_KEY=required-for-embeddings

# Async Processing
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
```

### Required Services

- **Qdrant**: Vector database (Docker or cloud)
- **Redis**: Celery broker and result backend
- **Celery Worker**: Background task processing

## Async Task Architecture

### Task Configuration

- **Max Retries**: 3 attempts per task
- **Retry Delay**: Exponential backoff (60s, 120s, 240s)
- **Task Binding**: Self-binding for retry access
- **Result Storage**: Task results stored in Redis backend

### Error Handling

- **Graceful Degradation**: Core incident operations never blocked
- **Comprehensive Logging**: All operations logged (no sensitive data)
- **Task Monitoring**: Status tracking via API endpoints
- **Failure Recovery**: Automatic retries with backoff

## Security Considerations

### Data Protection

- **No Sensitive Logging**: Incident content never logged
- **Environment Variables**: All secrets via environment configuration
- **Input Validation**: Comprehensive Pydantic model validation
- **Error Sanitization**: User-friendly error messages without system details

### API Security

- **Input Constraints**: Text length limits and validation
- **Rate Limiting**: Configurable via existing middleware
- **Authentication**: Inherits from main application auth

## Performance Characteristics

### Non-blocking Operations

- **Incident Creation**: ~50-100ms (database only)
- **Embedding Tasks**: 2-5 seconds (background)
- **Similarity Search**: 1-3 seconds (embedding + search)

### Scalability

- **Horizontal Scaling**: Multiple Celery workers
- **Queue Management**: Redis-based task distribution
- **Vector DB**: Qdrant handles millions of vectors

## Monitoring and Observability

### Key Metrics

- Task success/failure rates
- Embedding generation latency
- Vector DB operation performance
- Queue depth and processing time

### Log Messages

- Task trigger confirmations
- Embedding operation status
- Error conditions and recovery
- Performance timing data

## Best Practices

### Operational

- Monitor Celery worker health
- Track vector DB storage growth
- Regular embedding quality validation
- Backup vector collections

### Development

- Modular embedding logic for model swapping
- Comprehensive error handling at all layers
- Extensive documentation and logging
- Thorough testing of async operations

---

This architecture provides a robust, scalable foundation for semantic similarity search while maintaining the reliability and performance of the core incident management system.
