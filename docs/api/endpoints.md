# API Reference

The Incident Management System provides a comprehensive REST API built with FastAPI.

## Interactive Documentation

For complete, up-to-date API documentation with interactive testing:

- **Swagger UI**: `http://localhost:8000/docs` (development) or `https://your-domain.com/docs` (production)

## Base Configuration

- **Development**: `http://localhost:8000`
- **Production**: `https://your-domain.com`
- **API Title**: IncidentManagementAPI
- **Version**: 1.0.0

## Authentication

Most endpoints require JWT authentication. Include the token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

## Available Endpoints

The API includes the following endpoint groups:

### Core System

- **Health Check**: `GET /health` - System health status

### Authentication (`/auth`)

- User registration and login
- Token management and refresh
- Password reset functionality
- Rate limiting: 5 registrations per hour

### Projects (`/projects`)

- CRUD operations for projects
- Search and filtering
- Knowledge base association
- Project management

### Knowledge Base (`/knowledge-bases`)

- CRUD operations for knowledge bases
- Document management within knowledge bases
- File uploads, URL ingestion, and text content
- Document search and categorization

### Incidents (`/incidents`)

- CRUD operations for incidents
- Filtering and pagination
- Similar incident search
- Root cause analysis
- PDF report generation

### AI Agents (`/agents`)

- Chat interface with AI coordinator
- Specialized agent interactions

### Jobs (`/jobs`)

- Background job management
- GitHub integration tasks
- Job status tracking
- Running jobs monitoring

### Logs (`/logs`)

- Log fetching from Loki
- Label management
- Query-based filtering

### Users (`/users`)

- User profile management
- Password changes

### Dashboard (`/dashboard`)

- System metrics and statistics
- Key performance indicators
- Time-series data
- Resolution performance

### Data Bank (`/data_bank`)

- Vector database operations
- Similarity search functionality
- Embedding operations

### Runbooks (`/runbooks`)

- Runbook management and execution
- Step generation and tracking
- Incident-specific runbooks

### Events (`/events`)

- Incident event tracking
- Event CRUD operations
- Event history management

### Incident Metrics (`/incident/{incident_id}/metrics`)

- Performance metrics per incident
- Metric creation and updates
- Resolution time tracking

### Reports (`/incident/{incident_id}/report`)

- Incident report generation

## Response Format

All endpoints return JSON responses with consistent error handling:

```json
{
  "detail": "Error description",
  "status_code": 400
}
```

## Rate Limiting

- Registration endpoint: 5 requests per hour
- Standard rate limiting applies to other endpoints

## CORS Configuration

Development CORS origins:

- `http://localhost:5173`
- `http://localhost:2000`
- `http://127.0.0.1:5173`
- `http://127.0.0.1:2000`

## Detailed API Documentation

For complete endpoint specifications, request/response schemas, and interactive testing, visit the Swagger UI at `/docs`.
