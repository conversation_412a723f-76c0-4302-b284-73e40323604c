# Base image
FROM python:3.13-slim

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# Set working directory
WORKDIR /app

# Copy requirements file and install dependencies
COPY requirements.txt .

COPY --from=ghcr.io/astral-sh/uv:0.9.0 /uv /uvx /bin/

RUN uv pip install --no-cache-dir -r requirements.txt --system

# Install apt dependencies
RUN apt-get update && apt-get install -y --no-install-recommends curl gnupg ca-certificates docker.io git && \
    curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get clean

# Copy application code
COPY app /app/

# Start the FastAPI application
CMD ["uv", "run", "fastapi", "run", "main.py", "--workers", "4"]
