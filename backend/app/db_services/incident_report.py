from typing import Any, Dict, Optional
from uuid import UUID

from database.core import DbSession
from entities.incident import IncidentDetail
from entities.incident_report import IncidentReport


def get_incident_report(db: DbSession, incident_id: UUID) -> Optional[IncidentReport]:
    """Retrieve incident report by incident UUID."""
    return (
        db.query(IncidentReport)
        .filter(IncidentReport.incident_id == incident_id)
        .first()
    )


def get_incident_report_with_details(
    db: DbSession, incident_id: UUID
) -> Dict[str, Any]:
    """Retrieve incident report along with incident details by incident UUID."""
    result = (
        db.query(IncidentReport, IncidentDetail)
        .outerjoin(
            IncidentDetail, IncidentReport.incident_id == IncidentDetail.incident_id
        )
        .filter(IncidentReport.incident_id == incident_id)
        .first()
    )

    if not result:
        return {}

    incident_report, incident_detail = result

    impact_assessment = {
        "impact_forecast": incident_detail.impact_forecast if incident_detail else None,
        "cascading_risks": incident_detail.cascading_risks if incident_detail else None,
    }

    combined_data = {
        "incident_id": incident_report.incident_id,
        "executive_summary": incident_report.executive_summary,
        "post_incident_actions": incident_report.post_incident_actions or [],
        "retrospectives": incident_report.retrospectives or {},
        "action_items": incident_report.action_items or [],
        "approval_status": incident_report.approval_status,
        "comments": incident_report.comments,
        "approved_by": incident_report.approved_by,
        "approved_at": incident_report.approved_at,
        "root_cause": incident_detail.root_cause if incident_detail else None,
        "immediate_action": (
            incident_detail.immediate_action if incident_detail else None
        ),
        "impact_assessment": impact_assessment,
        "ai_generated": (
            incident_report.ai_generated
            if hasattr(incident_report, "ai_generated")
            else True
        ),
    }

    return combined_data
