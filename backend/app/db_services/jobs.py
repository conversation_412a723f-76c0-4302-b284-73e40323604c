from database.core import DbSession
from entities.job import Job, JobStatusEnum


def list_running_jobs(db: DbSession):
    return (
        db.query(Job)
        .filter(Job.status.in_([JobStatusEnum.PENDING, JobStatusEnum.STARTED]))
        .all()
    )


def create_job(db: DbSession, job: Job) -> Job:
    try:
        db.add(job)
        db.commit()
        db.refresh(job)
        return job
    except Exception as e:
        db.rollback()
        raise e


def list_all_jobs(db: DbSession, offset: int = 0, limit: int = 10):
    return db.query(Job).offset(offset).limit(limit).all()


def get_job_by_job_id(db: DbSession, job_id: str):
    return db.query(Job).filter(Job.job_id == job_id).first()
