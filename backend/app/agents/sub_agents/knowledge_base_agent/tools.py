from contextlib import contextmanager
from typing import Dict, List

from database.core import get_db
from google.adk.tools.mcp_tool.mcp_toolset import (
    MCPToolset,
    StdioConnectionParams,
    StdioServerParameters,
)


@contextmanager
def get_db_session():
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


official_doc_search_tool = MCPToolset(
    connection_params=StdioConnectionParams(
        server_params=StdioServerParameters(
            command="npx",
            args=["-y", "@upstash/context7-mcp"],
            env={},
        ),
        timeout=60,
    ),
)


def search_internal_docs(keywords: List[str], limit: int = 5) -> Dict:
    """Finds relevant documentation, runbooks, and knowledge base articles using intelligent keyword matching.

    This function searches the organization's knowledge base using advanced vector similarity matching
    to find documentation that's most relevant to the provided keywords. It's designed to support
    incident investigation by finding applicable runbooks, troubleshooting guides, system documentation,
    and operational procedures.

    Args:
        keywords (List[str]): List of search terms and keywords to match against documentation.
                             Should include:
                             - Technical terms (e.g., ["database", "connection", "timeout"])
                             - Service names (e.g., ["api-service", "user-authentication"])
                             - Error types (e.g., ["SSL", "certificate", "expired"])
                             - System components (e.g., ["kubernetes", "ingress", "load-balancer"])
                             More specific keywords yield better results.
        limit (int, optional): Maximum number of documents to return. Range: 1-20, Default: 5.
                              Use 3-5 for focused results, 10-20 for comprehensive research. No need to specifically ask the user for the limit.

    Returns:
        Dict[str, List[dict]]:
            status (str): "success" or "error"
            data (List[dict]): A list of dictionaries containing a ranked list of relevant documentation, ordered by relevance score. Each dictionary includes:
                - document_id (str): Unique identifier for the document
                - title (str): Document title or heading
                - content_preview (str): Relevant excerpt or summary
                - document_type (str): Type of document (runbook, guide, reference, etc.)
                - relevance_score (float): Similarity score (0.0-1.0, higher = more relevant)
                - tags (List[str]): Associated tags and categories
                - last_updated (str): ISO timestamp of last document update
                - url (str, optional): Link to full document if available

    Raises:
        ValueError: If keywords list is empty or contains only whitespace
        VectorSearchError: If vector search service is unavailable or returns errors
        Exception: For other unexpected errors during documentation search

    Example:
        >>> # Find SSL certificate documentation
        >>> docs = find_relevant_documentation(["SSL", "certificate", "renewal", "nginx"], limit=3)
        >>> for doc in docs:
        ...     print(f"{doc['title']} (Score: {doc['relevance_score']:.2f})")
        SSL Certificate Renewal Runbook (Score: 0.89)
        Nginx Configuration Guide (Score: 0.76)
        TLS Troubleshooting Procedures (Score: 0.71)

        >>> # Find database troubleshooting guides
        >>> docs = find_relevant_documentation(["database", "performance", "slow", "queries"])
        >>> if docs:
        ...     print(f"Found {len(docs)} relevant documents")
        ...     print(f"Top result: {docs[0]['title']}")

    Usage Guidelines:
        - Use specific, technical keywords related to the incident or question
        - Include service names, error types, and system components in keywords
        - Combine related terms for better context (e.g., ["API", "timeout", "load-balancer"])
        - Review relevance scores to assess document applicability (>0.7 typically most useful)
        - Check document_type to understand the kind of guidance provided
        - Use last_updated timestamp to ensure information currency
        - Start with focused searches (3-5 results), expand if needed
    """

    try:
        from vector_db.search_service import VectorSearchService

        search_service = VectorSearchService()
        with get_db_session() as db:
            similar_documents = search_service.search_similar_documents_by_text(
                db=db, text=" ".join(keywords), top_k=limit
            )

        return {"status": "success", "data": similar_documents}

    except Exception as e:
        return {"status": "error", "message": str(e)}
