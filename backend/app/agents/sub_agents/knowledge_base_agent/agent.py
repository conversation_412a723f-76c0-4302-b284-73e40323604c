from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm

from . import prompt, tools

# AGENT_MODEL = "ollama/qwen3:4b"
AGENT_MODEL = "gemini/gemini-2.0-flash"

root_agent = Agent(
    name="knowledge_base_agent",
    model=LiteLlm(AGENT_MODEL),
    description="Access knowledge base and provides relevant documentations about libraries, project information, system architecture and more.",
    instruction=prompt.INSTRUCTION,
    tools=[tools.official_doc_search_tool, tools.search_internal_docs],
)
