from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm

from . import prompt
from .tools import search_similar_documents

AGENT_MODEL = "gemini/gemini-2.0-flash"

root_agent = Agent(
    name="webhook_agent",
    model=LiteLlm(AGENT_MODEL),
    description="Create a helpful comment for issues reported in various channels.",
    instruction=prompt.INSTRUCTIONS,
    tools=[search_similar_documents],
)
