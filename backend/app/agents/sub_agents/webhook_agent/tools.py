import json
from contextlib import contextmanager
from uuid import UUID

from database.core import get_db
from db_services import documents as documents_db_service
from utils.logger import get_controller_logger
from vector_db.search_service import VectorSearchService

logger = get_controller_logger("webhook_agent")


@contextmanager
def get_db_session():
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


def search_similar_documents(issue_title: str, issue_body: str) -> str:
    """
    The function takes the user query as argument and returns the most similar documents from the knowledge base.
    """
    try:
        with get_db_session() as db:
            logger.info(f"data:{issue_title}")

            # Extract the relevant information for the search query
            title = issue_title
            body = issue_body
            query = f"issue_title:{title} , issue_body:{body}".strip()
            logger.info(f"Processing document similarity search for query: '{query}'")

            vector_service = VectorSearchService()
            logger.info(f"Vector search service initialized successfully")

            results = vector_service.search_similar_documents_by_text(
                db, text=query, top_k=5
            )
            indexed_results = []
            for index, result in enumerate(results):
                # Extract document and similarity_score from each result
                document = result["document"]
                # similarity_score = result['similarity_score']

                # Add to the list with automatic index
                indexed_results.append(
                    {
                        document
                        # 'index': index,
                        # 'document': document,
                        # 'similarity_score': similarity_score,
                    }
                )

            logger.info(f"Document search results with indexes: {indexed_results}")
            return {
                "results": indexed_results,
            }
    except Exception as e:
        raise Exception(
            f"Error inside document search tool of webhook agent: {str(e)}"
        ) from e
