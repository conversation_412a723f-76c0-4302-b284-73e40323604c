import json
from contextlib import contextmanager
from typing import Optional
from uuid import UUID

import instructor
import litellm
from database.core import get_db
from db_services import incident as incident_db_service
from pydantic import BaseModel
from utils.logger import get_service_logger
from vector_db.search_service import VectorSearchService

logger = get_service_logger("incident_manager")
from entities.incident import IncidentTypeEnum, PriorityEnum, SeverityEnum, StatusEnum


class StructuredIncidentData(BaseModel):
    title: str
    summary: str
    priority: str
    severity: str
    incident_type: str
    status: str = "OPEN"


def _structure_incident_data_with_llm(raw_input: str) -> dict:
    try:
        litellm.enable_json_schema_validation = True
        prompt = f"""
        Analyze the following incident description and extract structured information.

        Extract and format the following fields:
        - title: Short description of the issue (max 100 characters)
        - summary: Detailed explanation of the issue (max 500 characters)
        - priority: One of "P0", "P1", "P2", "P3", "P4" (based on urgency/blocking nature)
        - severity: One of "LOW", "ME<PERSON><PERSON>", "HIGH", "CR<PERSON><PERSON><PERSON>" (based on impact)
        - incident_type: One of "OUTAGE", "DEGRADATION", "SECURITY", "PERFORMANCE", "OTHER"
        - status: Always set to "OPEN" for new incidents

        Input: {raw_input}
        """

        client = instructor.from_litellm(litellm.completion)
        response = client.chat.completions.create(
            model="gemini/gemini-2.5-flash-lite-preview-06-17",
            messages=[{"role": "user", "content": prompt}],
            response_model=StructuredIncidentData,
        )

        structured_data = {
            "title": response.title,
            "summary": response.summary,
            "priority": getattr(PriorityEnum, response.priority, PriorityEnum.P0),
            "severity": getattr(SeverityEnum, response.severity, SeverityEnum.LOW),
            "incident_type": getattr(
                IncidentTypeEnum, response.incident_type, IncidentTypeEnum.OTHER
            ),
            "status": getattr(StatusEnum, response.status, StatusEnum.OPEN),
        }

        logger.info(f"LLM structured incident data: {structured_data}")
        return structured_data

    except Exception as e:
        logger.error(f"LLM structuring failed: {e}")
        return {
            "title": raw_input[:100] if len(raw_input) > 100 else raw_input,
            "summary": raw_input,
            "priority": PriorityEnum.P0,
            "severity": SeverityEnum.LOW,
            "incident_type": IncidentTypeEnum.OTHER,
            "status": StatusEnum.OPEN,
        }


@contextmanager
def get_db_session():
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


import random
import time
from datetime import datetime, timezone

from entities.incident import Incident, IncidentDetail, StatusEnum
from fastapi import HTTPException, status


def generate_incident_number() -> str:
    """Generate a unique incident number."""
    return f"INC-{int(time.time())}-{random.randint(1000, 9999)}"


def create_incident(incident_data: str):
    """Create a new incident passing in incident data as the argument"""
    with get_db_session() as db:
        try:
            if isinstance(incident_data, str):
                incident_data = incident_data.strip()
                if not incident_data:
                    raise ValueError("Incident data is empty, cannot parse JSON.")
                if incident_data.startswith("{"):
                    try:
                        incident_data = json.loads(incident_data)
                    except json.JSONDecodeError:
                        incident_data = _structure_incident_data_with_llm(incident_data)
                else:
                    incident_data = _structure_incident_data_with_llm(incident_data)
            elif not isinstance(incident_data, dict):
                raise ValueError("Incident data must be a dict or string")

            incident_number = generate_incident_number()
            incident = Incident(
                incident_number=incident_number,
                title=incident_data.get("title", ""),
                summary=incident_data.get("summary", ""),
                priority=incident_data.get("priority", PriorityEnum.P0),
                severity=incident_data.get("severity", SeverityEnum.LOW),
                incident_type=incident_data.get(
                    "incident_type", IncidentTypeEnum.OTHER
                ),
                status=incident_data.get("status", StatusEnum.OPEN),
                reported_by=None,
                reported_at=datetime.now(timezone.utc),
            )
            db.add(incident)
            db.flush()
            detail = IncidentDetail(
                incident_id=incident.id,
                affected_services=[],
                tags=[],
                incident_details="",
                attachments=[],
            )
            db.add(detail)
            db.flush()
            db.commit()
            db.refresh(incident)
            logger.info(f"Created incident {incident_number}")

            # Trigger async embedding task (non-blocking)
            try:
                from tasks.vector_db import upsert_incident_embedding_task

                # Prepare incident data for embedding - ensure we have meaningful content
                # Queue the async embedding task with just the incident ID
                task = upsert_incident_embedding_task.delay(str(incident.id))
                logger.info(
                    f"Triggered async embedding task {task.id} for incident {incident_number}"
                )

            except Exception as e:
                # Log but don't fail the main operation - embedding is not critical for incident creation
                logger.warning(
                    f"Failed to trigger embedding task for incident {incident_number}: {e}"
                )

        except Exception as e:
            db.rollback()
            logger.error(f"Failed to create incident: {str(e.__cause__)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create incident: {e.__cause__}",
            )


def get_recent_incidents(limit: int) -> list:
    """
    Retrieves a list of recent incidents.

    Args:
        limit: The maximum number of incidents to retrieve.

    Returns:
        A list of recent incidents.
    """
    with get_db_session() as db:
        incidents = incident_db_service.get_recent_incidents(db, limit)
        return incidents


def get_incident_details(
    incident_id: Optional[str] = None,
    incident_number: Optional[str] = None,
) -> dict:
    """Retrieves the details of a specific incident.

    Args:
        incident_id: The UUID of the incident to retrieve.
        incident_number: The incident number of the incident to retrieve.

    Returns:
        dict: A dictionary containing the incident details.

    Raises:
        ValueError: If neither incident_id nor incident_number is provided,
                   or if the incident is not found.
        Exception: For database or other unexpected errors.
    """
    if not incident_id and not incident_number:
        raise ValueError("Must specify either incident_id or incident_number")

    try:
        with get_db_session() as db:
            if incident_number:
                incident = incident_db_service.get_incident_by_number(
                    db, incident_number
                )
            elif incident_id:
                try:
                    incident_uuid = UUID(incident_id)
                except ValueError as e:
                    raise ValueError(
                        f"Invalid incident_id format: {incident_id}"
                    ) from e

                incident = incident_db_service.get_incident_by_id(db, incident_uuid)
            if not incident:
                raise ValueError(f"Incident not found with id: {incident_id}")

            result = {
                "incident_id": str(incident.id),
                "incident_number": incident.incident_number,
                "title": incident.title,
                "summary": incident.summary,
                "priority": incident.priority.value if incident.priority else None,
                "severity": incident.severity.value if incident.severity else None,
                "incident_type": (
                    incident.incident_type.value if incident.incident_type else None
                ),
                "status": incident.status.value if incident.status else None,
                "reported_at": (
                    incident.reported_at.isoformat() if incident.reported_at else None
                ),
                "updated_at": (
                    incident.updated_at.isoformat() if incident.updated_at else None
                ),
            }

            return result

    except ValueError:
        raise
    except Exception as e:
        raise Exception(f"Failed to retrieve incident details: {e}") from e


def search_similar_incidents_by_id(incident_id: str, top_k: int = 5):
    """
    Find similar incidents using vector similarity search passing in incident_id.

    Args:
        incident_id: UUID string of the incident to find matches for
        top_k: Number of similar incidents to return

    Returns:
        List[dict]: Similar incidents with their IDs, similarity scores, and basic info

    """
    try:
        with get_db_session() as db:
            incident = incident_db_service.get_incident_by_id(db, UUID(incident_id))
            if not incident:
                raise ValueError(f"Incident not found with id: {incident_id}")

            # Initialize vector search service
            search_service = VectorSearchService()

            # Get similar incidents using vector search
            similar_incidents = search_service.search_similar_incidents_by_id(
                db, incident_id=UUID(incident_id), top_k=top_k
            )

            return similar_incidents

    except Exception as e:
        raise Exception(f"Error finding similar incidents: {e}") from e


def search_similar_incidents_by_text(text: str, top_k: int = 5) -> dict:
    """
    Find similar incidents using vector similarity search passing in text.

    Args:
        text: Text to search for similar incidents
        top_k: Number of similar incidents to return

    Returns:
        dict: Similar incidents with their summaries, similarity scores, and other basic info

    """
    logger.info(
        f"Starting incident-only similarity search for text (length={len(text)} chars)"
    )
    try:
        with get_db_session() as db:
            search_service = VectorSearchService()
            similar_incidents = search_service.search_similar_incidents_by_text(
                db=db, text=text, top_k=top_k
            )
            results = []
            for item in similar_incidents:
                incident = item["incident"]
                result = {
                    "title": incident.title,
                    "summary": incident.summary,
                    "similarity_score": item["similarity_score"],
                }
                results.append(result)
            logger.info(f"Similar incidents found: {results}")
            return {
                "results": results,
            }
    except ValueError as ve:
        logger.error(f"ValueError during incident similarity search: {ve}")
        raise
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during incident similarity search: {e}"
        )
        # Re-raise to be handled by the controller as a server error (e.g., 5xx)
        raise
