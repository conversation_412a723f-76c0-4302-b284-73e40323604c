from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm

from . import prompt

AGENT_MODEL = "gemini/gemini-2.0-flash"
root_agent = LlmAgent(
    name="root_cause_analyzer",
    model=LiteLlm(AGENT_MODEL),
    description="Analyzes technical details of an incident to determine root cause, immediate action, impact forecast, and cascading risks.",
    instruction=prompt.INSTRUCTION,
    tools=[],
)
