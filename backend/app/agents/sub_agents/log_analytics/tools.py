import json
import re
from datetime import datetime

from google.adk.tools.tool_context import ToolContext
from litellm import completion
from routes.logs.models import LogQueryParams, LogsResponse


def get_current_datetime():
    """Gets the current date and time.
    Use this tool whenever you need the current date and time.

    Returns:
        str: The current date and time in the format 'YYYY-MM-DD HH:MM:SS'.
    """

    return f"Current date with time is {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"


async def fetch_logs(params: dict, tool_context: ToolContext) -> dict:
    """
    Fetches logs from the system based on a Loki query. Ensures the query is valid and retrieves logs within a specified time range.

    Args:
        params (dict): A dictionary containing the following parameters:
            query (str): The Loki query string to filter logs. Examples: '{job="system-logs"}', '{job="system-logs"} |= "Failed password"'.
                         The query must be a valid Loki query.
            start (str): The start timestamp for fetching logs in ISO 8601 format (e.g., 'YYYY-MM-DDTHH:MM:SSZ').
                         Example: '2025-06-24T10:00:00Z'.
            end (str): The end timestamp for fetching logs in ISO 8601 format (e.g., 'YYYY-MM-DDTHH:MM:SSZ').
                       Example: '2025-06-25T10:00:00Z'.
            limit (int, optional): The maximum number of logs to retrieve per request. Default is 100, maximum is 1000.
            direction (str, optional): The direction to retrieve logs, either "backward" (from end to start) or "forward" (from start to end).
                                       Default is "backward".
    Returns:
        LogsResponse: An object containing the retrieved logs.
    """
    from routes.logs import service as log_service

    try:
        if "query" not in params or not params["query"]:
            # Default query to fetch all logs if no filter is provided
            params["query"] = '{job=~".+"}'
        logResponse: LogsResponse = await log_service.fetch_logs_from_loki(
            LogQueryParams(**params)
        )
        tool_context.state["logs"] = json.dumps(
            [log.model_dump() for log in logResponse.data]
        )
        return {
            "status": "success",
            "message": "Logs fetched successfully and stored in tool_context.state['logs']",
            "logs": tool_context.state["logs"],
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to fetch logs: {e}",
        }


async def generate_summary(tool_context: ToolContext) -> str:
    """
    generates a summary of the logs provided in the tool_context.state['logs'] and return result as a string
    """
    response = completion(
        model="gemini/gemini-2.0-flash",
        messages=[
            {
                "role": "user",
                "content": f"Please provide a concise and accurate summary of the following logs:\n\n{tool_context.state['logs']}",
            }
        ],
    )
    if response and response.choices and response.choices[0].message.content:
        return response.choices[0].message.content.strip()
    return "No summary available."


async def generate_insights(tool_context: ToolContext) -> list[dict]:
    """
    Generate insights from the logs provided in the tool_context.state['logs'] and return result as an array of jsons with keys(title, description, logs)
    1. title: title of the insight
    2. description: description of the insight
    3. type: type of the insight (e.g., error, warning, info)
    4. logs: subset of logs from tool_context.state['logs'] that derives this

    The insights may be related to errors, warnings, or any significant events in the logs. Or it may be something affecting the system performance or security.
    It can also include patterns, anomalies, or trends observed in the logs that could be useful for further analysis or investigation.
    The insights should be concise, accurate, and focused on helping users quickly understand what the logs contain and identify any critical issues.
    """
    content = f"""
    Please provide a concise and accurate insights of the following logs:
    {tool_context.state["logs"]}

    The result should be an array of jsons with keys(title, description, logs). this is the format of result:
    {
        [
            {
                "title": "title of the insight",
                "description": "description of the insight",
                "type": "type of the insight",
                "logs": "subset of logs from {tool_context.state['logs']} that derives this insight",
            }
        ]
    }
    the final response should exactly be in this format
    """
    response = completion(
        model="gemini/gemini-2.0-flash",
        messages=[
            {
                "role": "user",
                "content": content,
            }
        ],
    )
    if response and response.choices and response.choices[0].message.content:
        try:
            json_string = response.choices[0].message.content.strip()
            pattern = r"^```json\s*(.*?)\s*```$"
            cleaned_string = re.sub(pattern, r"\1", json_string, flags=re.DOTALL)
            json_data = json.loads(cleaned_string.strip())
            if isinstance(json_data, list):
                return json_data
            else:
                return [
                    {
                        "title": "Error",
                        "description": "Insights should be an array of JSON objects.",
                    }
                ]

        except Exception as e:
            return [
                {"title": "Error", "description": f"Failed to parse insights: {str(e)}"}
            ]

    return [{"title": "Error", "description": "No insights available"}]


async def analyze_security_events(
    log_data: LogsResponse, tool_context: ToolContext
) -> dict:
    """Analyzes security events in logs to identify potential threats.

    Args:
        log_data: The log data to analyze.
        tool_context: Automatically provided by ADK, do not specify when calling.

    Returns:
        dict: A dictionary containing:
            - "status": "success" or "error"
            - If success: "findings" containing security analysis results
            - If error: "error_message" explaining what went wrong
    """
    # Implementation would analyze logs for security patterns
    # This is a placeholder
    return {"status": "success", "findings": "No suspicious security events detected"}
