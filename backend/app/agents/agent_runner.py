from core.config import config
from database.core import DbSession
from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>
from google.adk.agents import LlmAgent
from google.adk.artifacts import InMemoryArtifactService
from google.adk.memory import InMemoryMemoryService
from google.adk.plugins.logging_plugin import LoggingPlugin
from google.adk.runners import Runner
from google.adk.sessions import DatabaseSessionService, InMemorySessionService
from google.genai import types
from routes.auth.service import CurrentUser
from utils.logger import get_service_logger

logger = get_service_logger("agent_runner")
DB_URL = config.get_database_url()

_session_service: DatabaseSessionService | InMemorySessionService | None = None


def get_session_service() -> DatabaseSessionService | InMemorySessionService:
    """Get or create the session service instance."""
    global _session_service
    if _session_service is None:
        if DB_URL is None:
            logger.info("No DATABASE_URL env found, using in-memory session service.")
            _session_service = InMemorySessionService()
        else:
            try:
                logger.info("Attempting to use database session service")
                _session_service = DatabaseSessionService(db_url=DB_URL)
                logger.info("Database session service initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize database session service: {e}")
                logger.info("Falling back to in-memory session service")
                _session_service = InMemorySessionService()
    return _session_service


memory_service = InMemoryMemoryService()
artifact_service = InMemoryArtifactService()


async def get_or_create_session(
    app_name: str, user_id: str, session_id: str, initial_state: dict
):
    session_service = get_session_service()
    retrieved_session = await session_service.get_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    if retrieved_session:
        return retrieved_session
    return await session_service.create_session(
        app_name=app_name, user_id=user_id, session_id=session_id, state=initial_state
    )


def get_runner(app_name, agent) -> Runner:
    session_service = get_session_service()
    return Runner(
        agent=agent,
        app_name=app_name,
        session_service=session_service,
        memory_service=memory_service,
        artifact_service=artifact_service,
        plugins=[LoggingPlugin()],
    )


async def call_agent_async(query: str, runner: Runner, user_id: str, session_id: str):
    """Sends a query to the agent and prints the final response."""
    print(f"\n>>> User Query: {query}")

    content = types.Content(role="user", parts=[types.Part(text=query)])

    final_response_text = "Agent did not produce a final response."  # Default

    async for event in runner.run_async(
        user_id=user_id, session_id=session_id, new_message=content
    ):
        print(
            f"  [Event] Author: {event.author}, Type: {type(event).__name__}, Final: {event.is_final_response()}, Content: {event.content}"
        )

        if event.is_final_response():
            if event.content and event.content.parts and event.content.parts[0].text:
                final_response_text = event.content.parts[0].text
            elif event.actions and event.actions.escalate:
                final_response_text = (
                    f"Agent escalated: {event.error_message or 'No specific message.'}"
                )
            break  # Stop processing events once the final response is found

    print(f"<<< Agent Response: {final_response_text}")
    return final_response_text


async def handle_agent_request(
    db: DbSession, current_user: CurrentUser, query: str, agent: LlmAgent
) -> str:
    APP_NAME = "test_app"
    initial_state = {"user:preferences": {"language": "English"}}
    user_id = current_user.get_uuid()
    if not user_id:
        logger.warning("Unauthorized agent request attempt")
        raise HTTPException(status_code=401, detail="Unauthorized")
    logger.info(f"Processing agent request for user {user_id}")
    session_id = "session_001"  # Using a fixed ID for simplicity
    await get_or_create_session(APP_NAME, str(user_id), session_id, initial_state)
    runner = get_runner(APP_NAME, agent)
    response = await call_agent_async(query, runner, str(user_id), session_id)
    logger.info(f"Agent request processed for user {user_id}")
    return response


async def main():
    # Define constants for identifying the interaction context
    APP_NAME = "test_app"
    USER_ID = "user_1"
    SESSION_ID = "session_001"  # Using a fixed ID for simplicity

    """
    No prefix: Session-specific, persists only for the current session
    user:: User-specific, persists across all sessions for a particular user
    app:: Application-wide, shared across all users and sessions
    temp:: Temporary, exists only during the current execution cycle
    """
    initial_state = {"user:preferences": {"language": "English"}}
    await get_or_create_session(APP_NAME, USER_ID, SESSION_ID, initial_state)
    from agents.agent import root_agent

    runner = get_runner(APP_NAME, root_agent)
    print(f"Runner created for agent '{runner.agent.name}'.")

    while True:
        text = input("Enter your query: ")
        if text.lower() == "exit":
            break
        await call_agent_async(
            text, runner=runner, user_id=USER_ID, session_id=SESSION_ID
        )


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
