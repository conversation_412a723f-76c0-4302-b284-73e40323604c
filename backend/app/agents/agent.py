import google.genai.types as genai_types
from agents.guardrail import block_keyword_guardrail
from agents.sub_agents import (
    incident_manager_agent,
    knowledge_base_agent,
    log_analytics_agent,
    preference_agent,
    report_agent,
    root_cause_agent,
    runbook_generator_agent,
    time_agent,
)
from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from google.adk.planners import BuiltInPlanner
from google.adk.tools.agent_tool import AgentTool

from . import prompt

# AGENT_MODEL = "ollama/qwen3:4b"
AGENT_MODEL = "gemini/gemini-2.0-flash"
# AGENT_MODEL = "gemini/gemini-2.5-flash-preview-05-20"

root_agent = Agent(
    name="ai_assistant",
    model=LiteLlm(AGENT_MODEL),
    description="You are an AI based incident assistant. Your role is to assist the user in managing incidents by delegating tasks to the most appropriate specialized agents or utilizing available tools.",
    planner=BuiltInPlanner(
        thinking_config=genai_types.ThinkingConfig(
            include_thoughts=True, thinking_budget=-1
        )
    ),
    instruction=prompt.INSTRUCTION,
    sub_agents=[time_agent, preference_agent],
    tools=[
        AgentTool(agent=incident_manager_agent, skip_summarization=False),
        AgentTool(agent=knowledge_base_agent, skip_summarization=False),
        AgentTool(agent=log_analytics_agent, skip_summarization=False),
        AgentTool(agent=report_agent, skip_summarization=False),
        AgentTool(agent=root_cause_agent, skip_summarization=False),
        AgentTool(agent=runbook_generator_agent, skip_summarization=False),
    ],
    before_model_callback=block_keyword_guardrail,
)
