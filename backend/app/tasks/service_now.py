import base64
import logging
import os
from typing import Any, Dict

import requests
from google.genai import types
from utils.celery_worker import celery_app

logger = logging.getLogger(__name__)


@celery_app.task
def service_now_import(service_now_instance: str):
    print(f"Importing from ServiceNow instance: {service_now_instance}")


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def service_now_webhook(self, payload: Dict[str, Any]) -> Dict[str, Any]:
    def get_auth():
        username = os.getenv("SERVICENOW_USERNAME", "")
        password = os.getenv("SERVICENOW_PASSWORD", "")
        if not username or not password:
            raise ValueError("ServiceNow credentials not configured")
        creds = base64.b64encode(f"{username}:{password}".encode()).decode()
        return f"Basic {creds}"

    try:
        sys_id = payload.get("recordSysId")
        short_description = payload.get("shortDescription")
        description = payload.get("description", "No description provided")

        from agents.sub_agents import webhook_agent
        from google.adk.plugins.logging_plugin import LoggingPlugin
        from google.adk.runners import Runner
        from google.adk.sessions import InMemorySessionService

        session_service = InMemorySessionService()
        user_id = "system"
        session_id = f"servicenow_{sys_id}"

        session = session_service.create_session_sync(
            app_name="incident",
            user_id=user_id,
            session_id=session_id,
        )

        runner = Runner(
            agent=webhook_agent,
            app_name="incident",
            session_service=session_service,
            plugins=[LoggingPlugin()],
        )

        user_message = f"Issue Title: {short_description}\nIssue Body: {description}"

        content = types.Content(role="user", parts=[types.Part(text=user_message)])

        comment_text = ""
        for event in runner.run(
            user_id=user_id, session_id=session_id, new_message=content
        ):
            if event.is_final_response():
                if (
                    event.content
                    and event.content.parts
                    and event.content.parts[0].text
                ):
                    comment_text = event.content.parts[0].text
                break

        if not comment_text:
            logger.error("Agent failed to generate a comment.")
            comment_text = (
                "Incident processed by the system. No additional notes generated."
            )

        logger.info(f"Generated comment: {comment_text}")

        url = f"{os.getenv('SERVICENOW_INSTANCE_URL')}/api/now/table/incident/{sys_id}"
        headers = {
            "Authorization": get_auth(),
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        data = {"work_notes": comment_text}

        response = requests.patch(url, json=data, headers=headers)

        return {
            "status": "success",
            "message": "ServiceNow webhook processed successfully",
            "incident_sys_id": sys_id,
            "note_posted": True,
            "generated_comment": comment_text,
        }

    except Exception as e:
        logger.error(f"Error processing ServiceNow App webhook: {e}", exc_info=True)
        return {
            "status": "error",
            "error_type": "unexpected_error",
            "error_message": str(e),
        }
