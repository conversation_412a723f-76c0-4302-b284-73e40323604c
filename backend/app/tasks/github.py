import json
import os
import time
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional

from connectors import ConnectorError, GitHubConnector, RateLimitError
from google.genai import types
from utils.celery_worker import celery_app
from utils.logger import get_service_logger

logger = get_service_logger("github_tasks")


def get_github_config(repo_name: str) -> Dict[str, str]:
    """Get GitHub configuration with validation"""
    config = {
        "repo_name": repo_name,
    }
    token = os.getenv("GITHUB_ACCESS_TOKEN")
    if token:
        config["token"] = token
    installation_id = os.getenv("GITHUB_INSTALLATION_ID")  # Or fetch from DB if needed
    if installation_id:
        config["installation_id"] = installation_id
    # Do not raise error if token is missing; let the connector handle auth method
    return config


# Utility functions for task management
def validate_github_repo_format(repo_name: str) -> bool:
    """
    Validate GitHub repository name format (owner/repo)

    Args:
        repo_name: Repository name to validate

    Returns:
        True if valid format, False otherwise
    """
    if not repo_name or "/" not in repo_name:
        return False

    parts = repo_name.split("/")
    return len(parts) == 2 and all(part.strip() for part in parts)


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def github_import(
    self,
    github_repo: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    incident_type: str = "both",
) -> Dict[str, Any]:
    """
    Celery task to import issues from a GitHub repository.

    Args:
        github_repo: Repository name in format 'owner/repo'
        start_time: ISO format string for start time (optional, defaults to 15 days ago)
        end_time: ISO format string for end time (optional, defaults to now)
        incident_type: Type of issues to import ('open', 'closed', or 'both')

    Returns:
        Dict with task status and results
    """
    task_id = self.request.id
    logger.info(
        f"[IMPORT] Starting github_import task {task_id} for repo: {github_repo}"
    )
    logger.info(
        f"[IMPORT] Parameters - start_time: {start_time}, end_time: {end_time}, incident_type: {incident_type}"
    )

    try:
        # Validate repository format
        if not validate_github_repo_format(github_repo):
            raise ValueError("Invalid repository format. Expected 'owner/repo'")

        # Parse datetime parameters with defaults
        logger.debug("[IMPORT] Parsing datetime parameters...")
        start_dt = (
            datetime.fromisoformat(start_time)
            if start_time
            else datetime.now(timezone.utc) - timedelta(days=15)
        )
        end_dt = (
            datetime.fromisoformat(end_time) if end_time else datetime.now(timezone.utc)
        )
        logger.debug(f"[IMPORT] Parsed dates - start_dt={start_dt}, end_dt={end_dt}")

        # Validate date range
        if start_dt >= end_dt:
            raise ValueError("Start time must be before end time")

        # Validate incident_type
        if incident_type not in ("open", "closed", "both"):
            raise ValueError("incident_type must be 'open', 'closed', or 'both'")

        # Initialize connector with error handling
        logger.debug("[IMPORT] Initializing GitHubConnector...")
        config = get_github_config(github_repo)
        connector = GitHubConnector(config=config)
        logger.info("[IMPORT] GitHubConnector initialized successfully")

        # Define progress callback
        def progress_callback(
            current: int, total: int, success_count: int, message: str
        ):
            """Update task progress based on actual import progress"""
            self.update_state(
                state="STARTED",
                meta={
                    "current": current,
                    "total": total,
                    "success_count": success_count,
                    "status": message,
                    "repo": github_repo,
                    "incident_type": incident_type,
                },
            )

        # Update initial state
        logger.info("[IMPORT] Initializing GitHub import...")
        progress_callback(
            current=0,
            total=0,
            success_count=0,
            message="Initializing GitHub import...",
        )
        connector.register_callback(progress_callback)

        imported_incidents = connector.import_issues(start_dt, end_dt, incident_type)

        result = {
            "status": "success",
            "task_id": task_id,
            "repo": github_repo,
            "imported_count": len(imported_incidents),
            "incident_type": incident_type,
            "time_range": {"start": start_dt.isoformat(), "end": end_dt.isoformat()},
        }

        logger.info(
            f"[IMPORT] Import completed successfully. Imported {len(imported_incidents)} incidents"
        )
        return result

    except ValueError as e:
        logger.error(f"[IMPORT] Validation error in task {task_id}: {e}")
        return {
            "status": "error",
            "task_id": task_id,
            "error_type": "validation_error",
            "error_message": str(e),
        }

    except RateLimitError as e:
        logger.warning(f"[IMPORT] Rate limit exceeded in task {task_id}: {e}")
        logger.info("[IMPORT] Retrying task after rate limit cooldown...")
        raise self.retry(exc=e, countdown=300)  # Retry after 5 minutes

    except ConnectorError as e:
        logger.error(f"[IMPORT] Connector error in task {task_id}: {e}")
        return {
            "status": "error",
            "task_id": task_id,
            "error_type": "connector_error",
            "error_message": str(e),
        }

    except Exception as e:
        logger.exception(f"[IMPORT] Unexpected error in task {task_id}: {e}")
        return {
            "status": "error",
            "task_id": task_id,
            "error_type": "unexpected_error",
            "error_message": str(e),
        }


@celery_app.task(
    name="tasks.github.github_sync",
    bind=True,
    max_retries=3,
    default_retry_delay=60,
)
def github_sync(
    self,
    github_repo: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    incident_type: str = "both",
) -> Dict[str, Any]:
    """
    Celery task to sync GitHub issues with existing incidents.
    This task will update any existing incidents that match the GitHub issues.

    Args:
        github_repo: GitHub repository name in format 'owner/repo'
        start_time: Start time for sync window (ISO format, defaults to 7 days ago)
        end_time: End time for sync window (ISO format, defaults to now)
        incident_type: Type of issues to sync ('open', 'closed', or 'both')

    Returns:
        Dict containing sync status and number of updated incidents
    """
    task_id = self.request.id
    logger.info(f"[SYNC] Starting github sync task {task_id} for repo: {github_repo}")
    logger.info(
        f"[SYNC] Parameters - start_time: {start_time}, end_time: {end_time}, incident_type: {incident_type}"
    )

    try:
        # Validate repository format
        if not validate_github_repo_format(github_repo):
            raise ValueError("Invalid repository format. Expected 'owner/repo'")

        # Parse datetime parameters with defaults
        logger.debug("[SYNC] Parsing datetime parameters...")
        start_dt = (
            datetime.fromisoformat(start_time)
            if start_time
            else datetime.now(timezone.utc) - timedelta(days=7)
        )
        end_dt = (
            datetime.fromisoformat(end_time) if end_time else datetime.now(timezone.utc)
        )
        logger.debug(f"[SYNC] Parsed dates - start_dt={start_dt}, end_dt={end_dt}")

        # Validate date range
        if start_dt >= end_dt:
            raise ValueError("Start time must be before end time")

        # Validate incident_type
        if incident_type not in ("open", "closed", "both"):
            raise ValueError("incident_type must be 'open', 'closed', or 'both'")

        # Initialize connector with error handling
        logger.debug("[SYNC] Initializing GitHubConnector...")
        config = get_github_config(github_repo)
        connector = GitHubConnector(config=config)
        logger.info("[SYNC] GitHubConnector initialized successfully")

        # Define progress callback
        def progress_callback(
            current: int, total: int, success_count: int, message: str
        ):
            """Update task progress based on actual sync progress"""
            self.update_state(
                state="STARTED",
                meta={
                    "current": current,
                    "total": total,
                    "success_count": success_count,
                    "status": message,
                    "repo": github_repo,
                    "incident_type": incident_type,
                },
            )

        # Update initial state
        logger.info("[SYNC] Initializing GitHub sync...")
        progress_callback(
            current=0,
            total=0,
            success_count=0,
            message="Initializing GitHub sync...",
        )
        connector.register_callback(progress_callback)
        updated_incidents = connector.sync_issues(start_dt, end_dt, incident_type)

        result = {
            "status": "success",
            "task_id": task_id,
            "repo": github_repo,
            "updated_count": len(updated_incidents),
            "incident_type": incident_type,
            "time_range": {"start": start_dt.isoformat(), "end": end_dt.isoformat()},
        }

        logger.info(
            f"[SYNC] Sync completed successfully. Updated {len(updated_incidents)} incidents"
        )
        return result

    except ValueError as e:
        logger.error(f"[SYNC] Validation error in task {task_id}: {e}")
        return {
            "status": "error",
            "task_id": task_id,
            "error_type": "validation_error",
            "error_message": str(e),
        }

    except RateLimitError as e:
        logger.warning(f"[SYNC] Rate limit exceeded in task {task_id}: {e}")
        logger.info("[SYNC] Retrying task after rate limit cooldown...")
        raise self.retry(exc=e, countdown=300)  # Retry after 5 minutes

    except ConnectorError as e:
        logger.error(f"[SYNC] Connector error in task {task_id}: {e}")
        return {
            "status": "error",
            "task_id": task_id,
            "error_type": "connector_error",
            "error_message": str(e),
        }

    except Exception as e:
        logger.exception(f"[SYNC] Unexpected error in task {task_id}: {e}")

        if "rate limit" in str(e).lower():
            logger.warning("[SYNC] Detected rate limit in error message, retrying...")
            raise self.retry(exc=e, countdown=300)  # Retry after 5 minutes

        return {
            "status": "error",
            "task_id": task_id,
            "error_type": "unexpected_error",
            "error_message": str(e),
        }


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def github_webhook(self, payload: Dict[str, Any]) -> Dict[str, Any]:
    task_id = self.request.id
    logger.info(f"[WEBHOOK] Starting github webhook task {task_id}")
    try:
        if payload.get("action") != "opened" or "issue" not in payload:
            logger.info(
                "Webhook received for an event other than a new issue, skipping."
            )
            return {"status": "skipped", "reason": "not a new issue event"}

        repository = payload.get("repository", {}).get("full_name", "")
        repo_full_name = payload["repository"]["full_name"]
        issue = payload["issue"]
        issue_number = issue["number"]
        issue_title = issue["title"]
        issue_body = issue.get("body", "")
        session_id = f"github_issue_{issue_number}_{int(time.time())}"

        logger.info(
            f"Processing new issue #{issue_number} from repository {repo_full_name}"
        )

        from agents.sub_agents import webhook_agent
        from google.adk.plugins.logging_plugin import LoggingPlugin
        from google.adk.runners import Runner
        from google.adk.sessions import InMemorySessionService

        session_service = InMemorySessionService()
        user_id = repository.split("/")[0]

        session = session_service.create_session_sync(
            app_name="incident",
            user_id=user_id,
            session_id=session_id,
        )

        runner = Runner(
            agent=webhook_agent,
            app_name="incident",
            session_service=session_service,
            plugins=[LoggingPlugin()],
        )

        # Construct the user message for the agent
        user_message = f"Issue Title: {issue_title}\nIssue Body: {issue_body}"
        logger.info(f"User message for agent: {user_message}")

        content = types.Content(role="user", parts=[types.Part(text=user_message)])

        comment_text = ""
        for event in runner.run(
            user_id=user_id, session_id=session_id, new_message=content
        ):
            if event.is_final_response():
                if (
                    event.content
                    and event.content.parts
                    and event.content.parts[0].text
                ):
                    comment_text = event.content.parts[0].text
                break

        if not comment_text:
            logger.error("Agent failed to generate a comment.")
            raise ValueError("Generated comment is empty.")

        logger.info(f"Generated comment: {comment_text}")

        github_connector = GitHubConnector(
            config={
                "repo_name": repo_full_name,
            }
        )
        success = github_connector.post_comment(
            issue_id=issue_number, comment=comment_text
        )

        if success:
            logger.info("Successfully posted root cause analysis to GitHub.")
        else:
            logger.error(
                "Failed to post comment to GitHub. See connector logs for details."
            )

        return {"status": "success", "comment_posted": success}

    except Exception as e:
        logger.error(f"Error processing GitHub App webhook: {e}", exc_info=True)
        return {
            "status": "error",
            "task_id": task_id,
            "error_type": "unexpected_error",
            "error_message": str(e),
        }
