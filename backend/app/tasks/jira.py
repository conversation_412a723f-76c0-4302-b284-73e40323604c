import base64
import logging
import os
from typing import Any, Dict

import requests
from google.genai import types
from utils.celery_worker import celery_app

logger = logging.getLogger(__name__)


@celery_app.task
def jira_import(jira_project: str):
    print(f"Importing from Jira project: {jira_project}")


@celery_app.task(
    name="tasks.jira.jira_sync",
    bind=True,
    max_retries=3,
    default_retry_delay=60,
)
def jira_sync(jira_project: str):
    print(f"Syncing Jira project: {jira_project}")


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def jira_webhook(self, payload: Dict[str, Any]) -> Dict[str, Any]:
    try:
        issue = payload.get("issue", {})
        issue_key = issue.get("key")

        summary = issue.get("fields", {}).get("summary")
        description = issue.get("fields", {}).get("description")

        from agents.sub_agents import webhook_agent
        from google.adk.plugins.logging_plugin import LoggingPlugin
        from google.adk.runners import Runner
        from google.adk.sessions import InMemorySessionService

        session_service = InMemorySessionService()
        user_id = "system"
        session_id = f"servicenow_{issue_key}"

        session = session_service.create_session_sync(
            app_name="incident",
            user_id=user_id,
            session_id=session_id,
        )

        runner = Runner(
            agent=webhook_agent,
            app_name="incident",
            session_service=session_service,
            plugins=[LoggingPlugin()],
        )

        user_message = f"Issue Title: {summary}\nIssue Body: {description}"

        content = types.Content(role="user", parts=[types.Part(text=user_message)])

        comment_text = ""
        for event in runner.run(
            user_id=user_id, session_id=session_id, new_message=content
        ):
            if event.is_final_response():
                if (
                    event.content
                    and event.content.parts
                    and event.content.parts[0].text
                ):
                    comment_text = event.content.parts[0].text
                break

        if not comment_text:
            logger.error("Agent failed to generate a comment.")
            comment_text = (
                "Incident processed by the system. No additional notes generated."
            )

        logger.info(f"Generated comment: {comment_text}")

        url = f"{os.getenv('JIRA_BASE_URL')}/rest/api/3/issue/{issue_key}/comment"

        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        payload = {
            "body": {
                "type": "doc",
                "version": 1,
                "content": [
                    {
                        "type": "paragraph",
                        "content": [{"type": "text", "text": comment_text}],
                    }
                ],
            }
        }

        response = requests.post(
            url,
            auth=(os.getenv("JIRA_EMAIL"), os.getenv("JIRA_API_TOKEN")),
            headers=headers,
            json=payload,
            timeout=10.0,
        )

        return {
            "status": "success",
            "message": "Jira webhook processed successfully",
            "issue_id": issue_key,
            "comment_posted": True,
            "generated_comment": comment_text,
        }

    except Exception as e:
        logger.error(f"Error processing ServiceNow App webhook: {e}", exc_info=True)
        return {
            "status": "error",
            "error_type": "unexpected_error",
            "error_message": str(e),
        }
