import os
from typing import Optional

from dotenv import load_dotenv
from pydantic_settings import BaseSettings

load_dotenv()


class Config(BaseSettings):
    app_name: str = "Incident Management API"
    app_version: str = "1.0.0"
    debug: bool = False

    # Database configuration - use Pydantic settings for environment variables
    postgres_user: str = ""
    postgres_password: str = ""
    postgres_host: str = ""
    postgres_port: int = 5432
    postgres_db: str = ""

    # Allow direct database URL override (useful for tests)
    database_url: Optional[str] = None

    # CORS
    allowed_origins: list[str] = [
        "http://localhost:5173",
        "http://localhost:2000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:2000",
    ]
    allowed_methods: list[str] = ["*"]
    allowed_headers: list[str] = ["*"]
    allow_credentials: bool = True

    def get_database_url(self) -> str:
        """Get the database URL, either from direct setting or constructed from components."""
        # If DATABASE_URL is directly provided, use it (useful for tests)
        if self.database_url:
            return self.database_url

        # Otherwise, construct PostgreSQL URL from components
        if not self.postgres_user or not self.postgres_password or not self.postgres_host or not self.postgres_db:
            raise ValueError("Database configuration is incomplete. Either provide DATABASE_URL or all POSTGRES_* variables.")

        return f"postgresql+psycopg://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"

    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # Allow extra environment variables without validation errors


config = Config()
