import enum
import uuid

from database.core import Base
from sqlalchemy import Column, DateTime, Enum, ForeignKey, Text, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from .user import User


class JobStatusEnum(enum.Enum):
    PENDING = "pending"
    STARTED = "started"
    RETRY = "retry"
    SUCCESS = "success"
    FAILURE = "failure"
    REVOKED = "revoked"
    IGNORED = "ignored"


class JobTypeEnum(enum.Enum):
    GITHUB_IMPORT = "github_import"
    GITHUB_SYNC = "github_sync"
    GITHUB_WEBHOOK = "github_webhook"
    SERVICENOW_IMPORT = "servicenow_import"
    SERVICENOW_SYNC = "servicenow_sync"
    SERVICENOW_WEBHOOK = "servicenow_webhook"
    JIRA_IMPORT = "jira_import"
    JIRA_SYNC = "jira_sync"
    JIRA_WEBHOOK = "jira_webhook"
    INCIDENT_EMBEDDING_UPSERT = "incident_embedding_upsert"
    DOCUMENT_EMBEDDING_UPSERT = "document_embedding_upsert"


class Job(Base):
    __tablename__ = "jobs"

    job_id = Column(UUID(as_uuid=True), primary_key=True, nullable=False)
    job_type = Column(Enum(JobTypeEnum), nullable=False)
    status = Column(Enum(JobStatusEnum), nullable=False, default=JobStatusEnum.PENDING)
    message = Column(Text, nullable=True)
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        onupdate=func.now(),
        server_default=func.now(),
        nullable=False,
    )
    created_by_user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True
    )

    # Relationships
    created_by = relationship("User", back_populates="jobs")

    def __repr__(self):
        return f"<Job(job_id='{self.job_id}', job_type='{self.job_type}')>"
