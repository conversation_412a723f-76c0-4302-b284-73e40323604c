import enum
import uuid

from database.core import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, Boolean, Column, DateTime, Enum, ForeignKey, Text, func
from sqlalchemy.dialects.postgresql import UUID


class ApprovalStatusEnum(enum.Enum):
    PENDING = "PENDING"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"


class IncidentReport(Base):
    __tablename__ = "incident_report"

    executive_summary = Column(Text, primary_key=True)
    incident_id = Column(
        UUID(as_uuid=True),
        ForeignKey("incidents.id", ondelete="CASCADE"),
        nullable=False,
    )
    post_incident_actions = Column(JSON, nullable=True)
    retrospectives = Column(JSON, nullable=True)
    action_items = Column(JSON, nullable=True)
    approval_status = Column(
        Enum(ApprovalStatusEnum), nullable=False, default=ApprovalStatusEnum.PENDING
    )
    comments = Column(Text, nullable=True)
    approved_by = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True
    )
    approved_at = Column(DateTime(timezone=True), nullable=True)
    ai_generated = Column(Boolean, nullable=False, default=True)
    markdown_content = Column(Text, nullable=True, default="")
