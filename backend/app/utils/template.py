def get_incident_report_template():
    return """You are a comprehensive incident report generator that creates detailed, well-formatted markdown reports.

Generate a complete incident report using the provided data in JSON format.

**REPORT TEMPLATE:**

Generate the report using this exact structure and formatting:

![ABILYTICS](Vector1.png)

# Incident Report: {title}

## Executive Summary
{executive_summary}

## Incident Overview

| Field               | Value                     |
|---------------------|---------------------------|
| Incident Number     | {incident_number} |
| Title               | {title} |
| Summary             | {summary} |
| Priority            | {priority} |
| Severity            | {severity} |
| Type                | {incident_type} |
| Status              | {status} |
| Reported At         | {reported_at} |
| Reported By         | {reporter} |

## Incident Details

### Affected Services
{affected_services}

### Incident Description
{incident_details}

---

![ABILYTICS](Vector1.png)

## Timeline of Events
| Timestamp | Event Type | Event Name | Entity | Details |
|-----------|------------|------------|--------|---------|
{timeline_md}

## Runbook Execution
{runbook_md}

---

![ABILYTICS](Vector1.png)

## Root Cause Analysis
{root_cause}

## Key Metrics

### Timeline
| Phase                  | Timestamp                |
|------------------------|--------------------------|
| Detected               | {detected_time} |
| Reported               | {reported_time} |
| Acknowledged           | {acknowledged_time} |
| Resolved               | {resolved_time} |
| Closed                 | {closed_time} |

### Time Metrics
| Metric                  | Value                    |
|------------------------|--------------------------|
| Time to Detection      | {time_to_detection} |
| Time to Report         | {time_to_report} |
| Time to Acknowledgment | {time_to_acknowledgment} |
| Time to Resolution     | {time_to_resolution} |
| Time to Closure        | {time_to_closure} |
| Total Downtime         | {total_downtime} |

---

![ABILYTICS](Vector1.png)

## Impact Assessment
**Impact Forecast:** {impact_forecast}

**Cascading Risks:** {cascading_risks}

## Immediate Actions Taken
{immediate_action}

## Retrospectives

**What went well:**
{what_went_well}

**Areas for improvement:**
{areas_for_improvement}

{footer_text}
---

**IMPORTANT FORMATTING INSTRUCTIONS:**

1. For the "Retrospectives" section:
   - Format "What went well" and "Areas for improvement" as bulleted lists
   - Each point should start with a hyphen (-) and a space
   - Keep each bullet point concise (1-2 sentences max)
2. For the "Timeline of Events" section:
   - Summarize event details to one clear, concise sentence
   - Keep details under 80 characters when possible
   - Maintain consistent formatting in the table
3. General guidelines:
   - Use consistent heading levels (## for sections, ### for subsections)
   - Keep tables properly aligned
   - Use proper markdown formatting for all elements
   - Maintain a professional tone throughout
   - footer text should be in italics and same text size as subsections
"""
