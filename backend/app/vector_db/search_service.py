"""
Vector Search Service Module

Provides comprehensive vector operations for incidents and documents including
data retrieval, embedding generation, metadata creation, and vector storage/search.
This service handles all the technical complexity of vector operations internally.
"""

from typing import Annotated, Dict, List, Optional
from uuid import UUID

from database.core import DbSession
from db_services import documents as documents_db_service
from db_services import incident as incident_db_service
from db_services import incident_report as incident_report_db_service
from fastapi import Depends
from utils.logger import get_service_logger

from vector_db.base_connector import CollectionType
from vector_db.embeddings import generate_embedding
from vector_db.qdrant_connector import QdrantConnector

logger = get_service_logger("vector_search")


class VectorSearchService:
    """High-level vector search service for finding similar incidents and documents."""

    def __init__(self):
        self._connector = None

    @property
    def connector(self):
        """Lazy initialization of the QdrantConnector."""
        if self._connector is None:
            self._connector = QdrantConnector(
                collections={
                    CollectionType.INCIDENTS: "incidents",
                    CollectionType.DOCUMENTS: "documents",
                }
            )
        return self._connector

    def search_similar_incidents_by_text(
        self,
        db: DbSession,
        text: str,
        top_k: int = 5,
        exclude_ids: Optional[List[UUID]] = None,
    ) -> List[Dict]:
        """Search for incidents similar to given text."""
        if not text.strip():
            raise ValueError("Text cannot be empty")

        embedding = generate_embedding(text)
        results = self.connector._find_similar_vectors(
            CollectionType.INCIDENTS, embedding, top_k
        )

        return self._process_incident_results(db, results, exclude_ids=exclude_ids)

    def search_similar_incidents_by_id(
        self,
        db: DbSession,
        incident_id: UUID,
        top_k: int = 5,
        exclude_ids: Optional[List[UUID]] = None,
    ) -> List[Dict]:
        """Search for incidents similar to given incident ID."""
        incident = incident_db_service.get_incident_by_id(db, incident_id)
        if not incident:
            raise ValueError(f"Incident with ID {incident_id} not found")

        incident_dict = {
            "title": incident.title,
            "summary": incident.summary or "",
            "details": (
                incident.incident_detail.incident_details
                if incident.incident_detail
                else ""
            ),
        }
        embedding = generate_embedding(incident_dict)
        results = self.connector._find_similar_vectors(
            CollectionType.INCIDENTS, embedding, top_k + 1
        )

        # Add the source incident to exclusion list
        all_exclude_ids = [incident_id]
        if exclude_ids:
            all_exclude_ids.extend(exclude_ids)

        return self._process_incident_results(db, results, exclude_ids=all_exclude_ids)

    def search_similar_documents_by_text(
        self,
        db: DbSession,
        text: str,
        top_k: int = 5,
        exclude_ids: Optional[List[UUID]] = None,
    ) -> List[Dict]:
        """Search for documents similar to given text."""
        if not text.strip():
            raise ValueError("Text cannot be empty")

        embedding = generate_embedding(text)
        results = self.connector._find_similar_vectors(
            CollectionType.DOCUMENTS, embedding, top_k
        )
        logger.info(f"Similar document search results: {results}")
        return self._process_document_results(db, results, exclude_ids=exclude_ids)

    def _process_incident_results(
        self,
        db: DbSession,
        results: List[Dict],
        exclude_ids: Optional[List[UUID]] = None,
    ) -> List[Dict]:
        """Process vector search results into incident objects."""
        processed_results = []
        exclude_strings = [str(uid) for uid in exclude_ids] if exclude_ids else []

        for result in results:
            payload_incident_id = result["payload"].get("incident_id")
            if not payload_incident_id:
                continue

            if payload_incident_id in exclude_strings:
                continue

            try:
                incident_uuid = UUID(payload_incident_id)
                incident = incident_db_service.get_incident_by_id(db, incident_uuid)
                if incident:
                    processed_results.append(
                        {
                            "incident": incident,
                            "similarity_score": float(result["score"]),
                        }
                    )
            except (ValueError, TypeError):
                continue

        return processed_results

    def _process_document_results(
        self,
        db: DbSession,
        results: List[Dict],
        exclude_ids: Optional[List[UUID]] = None,
    ) -> List[Dict]:
        """Process document search results and add actual document information."""

        processed_results = []
        exclude_strings = [str(doc_id) for doc_id in exclude_ids] if exclude_ids else []
        for result in results:
            payload_document_id = result["payload"].get("document_id")
            if not payload_document_id:
                continue

            if payload_document_id in exclude_strings:
                continue

            try:
                document_uuid = UUID(payload_document_id)
                document = documents_db_service.get_document_by_id(db, document_uuid)
                logger.info(f"Document retrieved: {document}")
                if document:
                    processed_results.append(
                        {
                            "document": document.content,
                            "similarity_score": float(result["score"]),
                            "metadata": result["payload"],
                        }
                    )
            except (ValueError, TypeError):
                logger.error(f"Document processing failed: {payload_document_id}")
                continue
        logger.info("Documents processed successfully:{processed_results}")
        return processed_results

    def upsert_incident(self, db: DbSession, incident_id: UUID) -> bool:
        """
        Upsert an incident vector into the collection.

        Handles data retrieval, metadata creation, and embedding generation internally.

        Args:
            db (DbSession): Database session for data retrieval.
            incident_id (UUID): The unique identifier for the incident.

        Returns:
            bool: True if the upsert was successful, False otherwise.
        """
        # Retrieve incident data from database
        incident = incident_db_service.get_incident_by_id(db, incident_id)
        incident_detail = incident_db_service.get_incident_details(db, incident_id)
        incident_report = incident_report_db_service.get_incident_report(
            db, incident_id
        )

        if not incident:
            raise ValueError(f"Incident with ID {incident_id} not found")

        # Prepare incident data for embedding
        incident_dict = {
            "title": incident.title,
            "summary": incident.summary or "",
            "details": (
                incident.incident_detail.incident_details
                if incident.incident_detail
                else ""
            ),
        }
        logger.info(incident_dict)
        embedding = generate_embedding(incident_dict)

        metadata = {"incident_id": str(incident_id), "incident_title": incident.title}

        return self.connector._upsert_vector(
            CollectionType.INCIDENTS, incident_id, embedding, metadata
        )

    def upsert_incident_report(self, db: DbSession, incident_id: UUID) -> bool:
        incident_detail = incident_db_service.get_incident_details(db, incident_id)
        incident_report = incident_report_db_service.get_incident_report(
            db, incident_id
        )

        if not incident_report:
            raise ValueError(f"Incident report with ID {incident_id} not found")

        incident_report_dict = {
            "root_cause": incident_detail.root_cause,
            "impact_forecast": incident_detail.impact_forecast,
            "immediate_action": incident_detail.immediate_action,
            "cascading_risks": incident_detail.cascading_risks,
            "executive_summary": incident_report.executive_summary,
            "affected_services": incident_detail.affected_services,
        }
        logger.info(incident_report_dict)
        # Generate embedding from incident data
        embedding = generate_embedding(incident_report_dict)

        # Create metadata
        metadata = {"incident_id": str(incident_id), "incident_title": incident.title}

        return self.connector._upsert_vector(
            CollectionType.INCIDENTS, incident_id, embedding, metadata
        )

    def upsert_document(self, db: DbSession, document_id: UUID) -> bool:
        """
        Upsert a document vector into the collection.

        Handles data retrieval, metadata creation, and embedding generation internally.

        Args:
            db (DbSession): Database session for data retrieval.
            document_id (UUID): The unique identifier for the document.

        Returns:
            bool: True if the upsert was successful, False otherwise.
        """
        # Retrieve document from database
        document = documents_db_service.get_document_by_id(db, document_id)
        if not document:
            raise ValueError(f"Document with ID {document_id} not found")

        # Prepare document data for embedding
        document_dict = {
            "name": document.name,
            "description": document.description or "",
            "content": document.content or "",
            "document_type": document.document_type,
        }

        # Generate embedding from document data
        embedding = generate_embedding(document_dict)

        # Create metadata with comprehensive document information
        metadata = {
            "document_id": str(document_id),
            "document_name": document.name,
            "document_type": document.document_type,
            "knowledge_base_id": str(document.knowledge_base_id),
            "created_at": (
                document.created_at.isoformat() if document.created_at else None
            ),
            "updated_at": (
                document.updated_at.isoformat() if document.updated_at else None
            ),
        }

        return self.connector._upsert_vector(
            CollectionType.DOCUMENTS, document_id, embedding, metadata
        )

    def delete_incident_vector(self, incident_id: UUID) -> bool:
        """Delete an incident vector from the collection."""
        return self.connector._delete_vector(CollectionType.INCIDENTS, incident_id)

    def delete_document_vector(self, document_id: UUID) -> bool:
        """Delete a document vector from the collection."""
        return self.connector._delete_vector(CollectionType.DOCUMENTS, document_id)


def get_vector_service():
    """Dependency injection function for VectorSearchService."""
    return VectorSearchService()


# Annotated dependency for FastAPI dependency injection
VectorService = Annotated["VectorSearchService", Depends(get_vector_service)]
