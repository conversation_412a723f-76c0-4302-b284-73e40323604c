import os
import sys
import time
from contextlib import asynccontextmanager

from core.config import config
from database.core import Base, engine
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from routes import register_routes
from utils.logger import get_app_logger, get_middleware_logger

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

logger = get_app_logger("main")
middleware_logger = get_middleware_logger()


@asynccontextmanager
async def lifespan(_app: FastAPI):
    # Startup
    logger.info("Application startup")
    yield
    # Shutdown (if needed in the future)


app = FastAPI(title=config.app_name, version=config.app_version, lifespan=lifespan)


# Add request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()

    # Log the incoming request (only for non-health checks to reduce noise)
    if request.url.path != "/health":
        client_ip = request.client.host if request.client else "unknown"
        middleware_logger.info(f"{request.method} {request.url.path} from {client_ip}")

    response = await call_next(request)

    # Log the response with timing
    process_time = time.time() - start_time
    if request.url.path != "/health" or response.status_code >= 400:
        middleware_logger.info(
            f"{request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s"
        )

    return response


# Add GZip middleware to enable response compression
app.add_middleware(GZipMiddleware, minimum_size=500, compresslevel=9)

# Add CORS middleware for development
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.allowed_origins,
    allow_credentials=config.allow_credentials,
    allow_methods=config.allowed_methods,
    allow_headers=config.allowed_headers,
)
Base.metadata.create_all(bind=engine)


@app.get("/health")
async def health():
    return {"status": "ok"}


register_routes(app)
