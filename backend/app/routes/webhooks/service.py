import hashlib
import hmac
import json
from uuid import UUID

from database.core import DbSession
from db_services import jobs as jobs_db_service
from entities.job import Job, JobStatusEnum, JobTypeEnum
from fastapi import HTTPException, status
from utils.logger import get_service_logger

logger = get_service_logger("webhook")


def verify_github_webhook_signature(payload_body, secret_token, signature_header):
    """Verify that the payload was sent from GitHub by validating SHA256.

    Raise and return 403 if not authorized.

    Args:
        payload_body: original request body to verify (request.body())
        secret_token: GitHub app webhook token (WEBHOOK_SECRET)
        signature_header: header received from GitHub (x-hub-signature-256)
    """
    if not signature_header:
        raise HTTPException(
            status_code=403, detail="x-hub-signature-256 header is missing!"
        )
    hash_object = hmac.new(
        secret_token.encode("utf-8"), msg=payload_body, digestmod=hashlib.sha256
    )
    expected_signature = "sha256=" + hash_object.hexdigest()
    if not hmac.compare_digest(expected_signature, signature_header):
        raise HTTPException(status_code=403, detail="Request signatures didn't match!")


def handle_github_webhook(db: DbSession, payload: dict) -> str:
    logger.info("Processing GitHub webhook")

    try:
        logger.info(f"Full payload:\n{json.dumps(payload, indent=2)}")

        from tasks.github import github_webhook

        task = github_webhook.delay(payload)

        logger.info(f"[GITHUB_WEBHOOK] Task created successfully with ID: {task.id}")

        job_data = Job(
            job_id=UUID(task.id),
            job_type=JobTypeEnum.GITHUB_WEBHOOK,
            status=JobStatusEnum.PENDING,
            created_by_user_id=None,
        )

        job = jobs_db_service.create_job(db, job_data)
        logger.info(f"Successfully created job with job_id: {job.job_id}")
        return job

    except json.JSONDecodeError as e:
        logger.error(f"JSON decoding error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid JSON payload"
        )
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error processing payload",
        )


def handle_jira_webhook(db: DbSession, payload: dict) -> str:
    logger.info("Processing Jira webhook")

    try:
        logger.info(f"Full payload:\n{json.dumps(payload, indent=2)}")

        from tasks.jira import jira_webhook

        task = jira_webhook.delay(payload)

        logger.info(f"[JIRA_WEBHOOK] Task created successfully with ID: {task.id}")

        job_data = Job(
            job_id=UUID(task.id),
            job_type=JobTypeEnum.JIRA_WEBHOOK,
            status=JobStatusEnum.PENDING,
            created_by_user_id=None,
        )

        job = jobs_db_service.create_job(db, job_data)
        logger.info(f"Successfully created job with job_id: {job.job_id}")
        return job

    except json.JSONDecodeError as e:
        logger.error(f"JSON decoding error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid JSON payload"
        )
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error processing payload",
        )


def handle_servicenow_webhook(db: DbSession, payload: dict):
    logger.info("Processing servicenow webhook")

    try:
        from tasks.service_now import service_now_webhook

        task = service_now_webhook.delay(payload)

        logger.info(
            f"[ServiceNow_WEBHOOK] Task created successfully with ID: {task.id}"
        )

        job_data = Job(
            job_id=UUID(task.id),
            job_type=JobTypeEnum.SERVICENOW_WEBHOOK,
            status=JobStatusEnum.PENDING,
            created_by_user_id=None,
        )

        job = jobs_db_service.create_job(db, job_data)
        logger.info(f"Successfully created job with job_id: {job.job_id}")
        return job

    except json.JSONDecodeError as e:
        logger.error(f"JSON decoding error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid JSON payload"
        )
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error processing payload",
        )
