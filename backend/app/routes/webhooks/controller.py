import json
import os

from database.core import DbSession
from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException, Request, status
from utils.logger import get_controller_logger

from routes.webhooks import service

router = APIRouter(prefix="/webhooks", tags=["Webhooks"])
logger = get_controller_logger("webhook")


@router.post("/github_webhook", status_code=status.HTTP_200_OK)
async def github_webhook(
    db: DbSession, request: Request, x_hub_signature_256: str = Header(...)
):
    logger.info("Received GitHub webhook request")

    try:
        secret_token = os.getenv("GITHUB_WEBHOOK_SECRET")
        if not secret_token:
            logger.warning(
                "GITHUB_WEBHOOK_SECRET is not set in environment variables, skipping signature verification"
            )
            payload = await request.json()
        else:
            payload_body = await request.body()
            service.verify_github_webhook_signature(
                payload_body=payload_body,
                secret_token=secret_token,
                signature_header=x_hub_signature_256,
            )
            payload = json.loads(payload_body.decode("utf-8"))
        result = service.handle_github_webhook(db, payload)
        return {"message": "GitHub webhook processed", "status": result}
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Unhandled exception in github_webhook: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/jira_webhook", status_code=status.HTTP_200_OK)
async def jira_webhook(db: DbSession, request: Request):
    logger.info("Received Jira webhook request")

    try:
        payload = await request.json()

        result = service.handle_jira_webhook(db, payload)
        return {"message": "Jira webhook processed", "status": result}
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Unhandled exception in jira_webhook: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/servicenow_webhook", status_code=status.HTTP_200_OK)
async def jira_webhook(db: DbSession, request: Request):
    logger.info("Received ServiceNow webhook request")

    try:
        payload = await request.json()

        result = service.handle_servicenow_webhook(db, payload)
        return {"message": "servicenow webhook processed", "status": result}
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Unhandled exception in servicenow_webhook: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
