from typing import Optional
from uuid import UUID

from database.core import DbSession
from entities.job import JobStatusEnum, JobTypeEnum
from fastapi import APIRouter, HTTPException, status
from routes.auth.service import CurrentUser
from routes.jobs import service
from routes.jobs.models import (
    JobResponse,
    JobStatusResponse,
    PaginatedResponse,
)
from tasks import github
from utils.logger import get_controller_logger

logger = get_controller_logger("jobs")
router = APIRouter(prefix="/jobs", tags=["Jobs"])


@router.get("", response_model=PaginatedResponse)
async def list_jobs(
    db: DbSession,
    current_user: CurrentUser,
    offset: int = 0,
    limit: int = 10,
):
    logger.info("Listing jobs")
    try:
        result = service.list_jobs(db, current_user, offset, limit)
        logger.info(f"Successfully listed {len(result.items)} jobs")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to list jobs: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to list jobs")


@router.get("/running", response_model=PaginatedResponse)
async def list_running_jobs(db: DbSession, current_user: CurrentUser):
    logger.info("Listing running jobs")
    try:
        result = service.list_running_jobs(db, current_user)
        logger.info(f"Successfully listed {len(result.items)} running jobs")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to list running jobs: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to list running jobs")


@router.post(
    "/import_github", status_code=status.HTTP_202_ACCEPTED, response_model=JobResponse
)
async def import_from_github(
    db: DbSession,
    current_user: CurrentUser,
    github_repo: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
):
    logger.info(
        f"/import_github route called with github_repo={github_repo}, start_time={start_time}, end_time={end_time}"
    )
    try:
        task = github.github_import.delay(github_repo, start_time, end_time)
        logger.info(f"GitHub import job submitted with task ID: {task.id}")
        job_data = {
            "job_id": UUID(task.id),
            "job_type": JobTypeEnum.GITHUB_IMPORT,
            "status": JobStatusEnum.PENDING,
        }
        job = service.create_job(db, current_user, job_data)
        logger.info(f"Successfully created job with job_id: {job.job_id}")
        return job
    except Exception as e:
        logger.error(f"Failed to start GitHub import job for {github_repo}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to start GitHub import job")


@router.post(
    "/sync_github", status_code=status.HTTP_202_ACCEPTED, response_model=JobResponse
)
async def sync_from_github(
    db: DbSession,
    current_user: CurrentUser,
    github_repo: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    incident_type: str = "both",
):
    """
    Sync GitHub issues with existing incidents in the database.
    This will update any existing incidents that match the GitHub issues.

    Args:
        github_repo: GitHub repository name (e.g., 'pytorch/pytorch')
        start_time: Start time for the sync window (ISO format)
        end_time: End time for the sync window (ISO format)
        incident_type: Type of issues to sync ('open', 'closed', or 'both')

    Returns:
        JobResponse containing job information
    """
    logger.info(
        f"[SYNC_GITHUB] Starting sync request for repo={github_repo}, start_time={start_time}, end_time={end_time}, incident_type={incident_type}"
    )
    try:
        logger.debug("[SYNC_GITHUB] Creating Celery task for github_sync")
        task = github.github_sync.delay(
            github_repo, start_time, end_time, incident_type
        )
        logger.info(f"[SYNC_GITHUB] Task created successfully with ID: {task.id}")

        job_data = {
            "job_id": UUID(task.id),
            "job_type": JobTypeEnum.GITHUB_SYNC,
            "status": JobStatusEnum.PENDING,
        }
        job = service.create_job(db, current_user, job_data)
        logger.info(f"Successfully created job with job_id: {job.job_id}")
        return job
    except Exception as e:
        logger.error(
            f"[SYNC_GITHUB] Failed to create sync task for {github_repo}: {str(e)}"
        )
        raise HTTPException(status_code=500, detail="Failed to start GitHub sync job")


# @router.post("/fetch_github_readme", status_code=status.HTTP_202_ACCEPTED)
# async def fetch_readme(github_repo: str):
#     logger.info(f"Starting GitHub README fetch job for repository: {github_repo}")
#     try:
#         task = github.fetch_github_readme.delay(github_repo)
#         logger.info(f"GitHub README fetch job submitted with task ID: {task.id}")
#         return {"status": "submitted", "task_id": task.id}
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(
#             f"Failed to start GitHub README fetch job for {github_repo}: {str(e)}"
#         )
#         raise HTTPException(
#             status_code=500, detail="Failed to start GitHub README fetch job"
#         )


@router.get("/status/{job_id}", response_model=JobStatusResponse)
async def get_job_status(db: DbSession, current_user: CurrentUser, job_id: str):
    logger.info(f"Getting job status for job ID: {job_id}")
    try:
        result = service.get_job_status(db, current_user, job_id)
        logger.info(f"Successfully retrieved job status for job ID: {job_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job status for job ID {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get job status")
