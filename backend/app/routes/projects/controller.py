from uuid import UUID

from database.core import DbSession
from fastapi import APIRouter, Query, status
from utils.logger import get_controller_logger

from routes.auth.service import CurrentUser
from routes.projects import models, service

logger = get_controller_logger("projects")
router = APIRouter(prefix="/projects", tags=["Projects"])


@router.post(
    "", response_model=models.ProjectResponse, status_code=status.HTTP_201_CREATED
)
def create_project(
    data: models.ProjectCreate, db: DbSession, current_user: CurrentUser
):
    """Create a new project."""
    logger.info(f"Creating project for user {current_user.get_uuid()}: {data.name}")
    try:
        result = service.create_project(db, data, current_user.get_uuid())
        logger.info(f"Successfully created project with ID: {result.id}")
        return result
    except Exception as e:
        logger.error(f"Failed to create project: {str(e)}")
        raise


@router.get("", response_model=models.PaginatedProjectResponse)
def get_user_projects(
    db: DbSession,
    current_user: CurrentUser,
    offset: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
):
    """Get projects for the current user."""
    logger.info(
        f"Getting projects for user {current_user.get_uuid()} with offset={offset}, limit={limit}"
    )
    try:
        projects, total = service.get_user_projects(
            db, current_user.get_uuid(), offset, limit
        )
        pages = (total + limit - 1) // limit
        page = (offset // limit) + 1

        # Transform projects to include knowledge base count
        project_responses = []
        for project in projects:
            kb_count = len(project.knowledge_bases) if project.knowledge_bases else 0
            project_response = models.ProjectListResponse(
                id=project.id,
                name=project.name,
                description=project.description,
                created_at=project.created_at,
                updated_at=project.updated_at,
                created_by=project.created_by,
                knowledge_base_count=kb_count,
            )
            project_responses.append(project_response)

        logger.info(f"Retrieved {len(projects)} projects out of {total} total")
        return {
            "items": project_responses,
            "total": total,
            "page": page,
            "limit": limit,
            "pages": pages,
        }
    except Exception as e:
        logger.error(f"Failed to get projects: {str(e)}")
        raise


@router.get("/search", response_model=models.PaginatedProjectResponse)
def search_projects(
    search: str = Query(..., description="Search term"),
    db: DbSession = None,
    current_user: CurrentUser = None,
    offset: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
):
    """Search projects by name."""
    logger.info(
        f"Searching projects with term '{search}' with offset={offset}, limit={limit}"
    )
    try:
        projects, total = service.search_projects(db, search, offset, limit)
        pages = (total + limit - 1) // limit
        page = (offset // limit) + 1

        # Transform projects to include knowledge base count
        project_responses = []
        for project in projects:
            kb_count = len(project.knowledge_bases) if project.knowledge_bases else 0
            project_response = models.ProjectListResponse(
                id=project.id,
                name=project.name,
                description=project.description,
                created_at=project.created_at,
                updated_at=project.updated_at,
                created_by=project.created_by,
                knowledge_base_count=kb_count,
            )
            project_responses.append(project_response)

        logger.info(
            f"Found {len(projects)} projects out of {total} total matching '{search}'"
        )
        return {
            "items": project_responses,
            "total": total,
            "page": page,
            "limit": limit,
            "pages": pages,
        }
    except Exception as e:
        logger.error(f"Failed to search projects: {str(e)}")
        raise


@router.get("/{project_id}", response_model=models.ProjectResponse)
def get_project(project_id: UUID, db: DbSession, current_user: CurrentUser):
    """Get a project by ID."""
    logger.info(f"Getting project: {project_id}")
    try:
        project = service.get_project_by_id(db, project_id)

        # Transform knowledge bases to include document count
        kb_info = []
        if project.knowledge_bases:
            for kb in project.knowledge_bases:
                doc_count = len(kb.documents) if kb.documents else 0
                kb_response = models.KnowledgeBaseInfo(
                    id=kb.id,
                    name=kb.name,
                    kb_type=kb.kb_type,
                    description=kb.description,
                    document_count=doc_count,
                )
                kb_info.append(kb_response)

        result = models.ProjectResponse(
            id=project.id,
            name=project.name,
            description=project.description,
            created_at=project.created_at,
            updated_at=project.updated_at,
            created_by=project.created_by,
            knowledge_bases=kb_info,
        )

        logger.info(f"Successfully retrieved project: {project_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to get project {project_id}: {str(e)}")
        raise


@router.put("/{project_id}", response_model=models.ProjectResponse)
def update_project(
    project_id: UUID,
    data: models.ProjectUpdate,
    db: DbSession,
    current_user: CurrentUser,
):
    """Update a project."""
    logger.info(f"Updating project: {project_id}")
    try:
        result = service.update_project(db, project_id, data, current_user.get_uuid())
        logger.info(f"Successfully updated project: {project_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to update project {project_id}: {str(e)}")
        raise


@router.delete("/{project_id}")
def delete_project(
    project_id: UUID,
    db: DbSession,
    current_user: CurrentUser,
):
    """Delete a project."""
    logger.info(f"Deleting project: {project_id}")
    try:
        result = service.delete_project(db, project_id, current_user.get_uuid())
        logger.info(f"Successfully deleted project: {project_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to delete project {project_id}: {str(e)}")
        raise
