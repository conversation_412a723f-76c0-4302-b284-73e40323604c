import os
from typing import List, Optional
from uuid import UUID

from database.core import DbSession
from fastapi import APIRouter, File, Form, HTTPException, Query, UploadFile, status
from fastapi.responses import FileResponse
from utils.logger import get_controller_logger

from routes.auth.service import CurrentUser
from routes.knowledge_base import models, service

logger = get_controller_logger("knowledge-bases")
router = APIRouter(prefix="/knowledge-bases", tags=["Knowledge Bases"])


@router.post(
    "", response_model=models.KnowledgeBaseResponse, status_code=status.HTTP_201_CREATED
)
def create_knowledge_base(
    data: models.KnowledgeBaseCreate, db: DbSession, current_user: CurrentUser
):
    """Create a new knowledge base."""
    logger.info(
        f"Creating knowledge base for user {current_user.get_uuid()}: {data.name}"
    )
    try:
        result = service.create_kb_entry(db, data, current_user.get_uuid())
        logger.info(f"Successfully created knowledge base with ID: {result.id}")
        return result
    except Exception as e:
        logger.error(f"Failed to create knowledge base: {str(e)}")
        raise


@router.get("", response_model=List[models.KnowledgeBaseResponse])
def get_knowledge_bases(db: DbSession, current_user: CurrentUser, project_id: UUID):
    """Get all knowledge bases with pagination."""
    logger.info(f"Getting knowledge bases for project {project_id}")
    try:
        knowledge_bases = service.get_kb_entries_by_project_id(db, project_id)
        if not knowledge_bases:
            logger.warning(f"No knowledge bases found for project {project_id}")
            return []
        logger.info(f"Retrieved {len(knowledge_bases)} knowledge bases")
        return knowledge_bases

    except Exception as e:
        logger.error(f"Failed to get knowledge bases: {str(e)}")
        raise


@router.get("/{kb_id}", response_model=models.KnowledgeBaseResponse)
def get_knowledge_base(kb_id: UUID, db: DbSession, current_user: CurrentUser):
    """Get a knowledge base by ID."""
    logger.info(f"Getting knowledge base: {kb_id}")
    try:
        result = service.get_kb_entry_by_id(db, kb_id)

        # Transform documents to include basic info
        doc_info = []
        if result.documents:
            for doc in result.documents:
                doc_response = models.DocumentInfo(
                    id=doc.id,
                    name=doc.name,
                    document_type=doc.document_type,
                    sync_status=doc.sync_status,
                    created_at=doc.created_at,
                )
                doc_info.append(doc_response)

        # Create response with proper enum handling
        response = models.KnowledgeBaseResponse(
            id=result.id,
            name=result.name,
            description=result.description,
            kb_type=(
                result.kb_type.value
                if hasattr(result.kb_type, "value")
                else result.kb_type
            ),
            project_id=result.project_id,
            created_at=result.created_at,
            updated_at=result.updated_at,
            created_by=result.created_by,
            updated_by=result.updated_by,
            documents=doc_info,
        )

        logger.info(f"Successfully retrieved knowledge base: {kb_id}")
        return response
    except Exception as e:
        logger.error(f"Failed to get knowledge base {kb_id}: {str(e)}")
        raise


@router.put("/{kb_id}", response_model=models.KnowledgeBaseResponse)
def update_knowledge_base(
    kb_id: UUID,
    data: models.KnowledgeBaseUpdate,
    db: DbSession,
    current_user: CurrentUser,
):
    """Update a knowledge base."""
    logger.info(f"Updating knowledge base: {kb_id}")
    try:
        result = service.update_kb_entry(db, kb_id, data, current_user.get_uuid())
        logger.info(f"Successfully updated knowledge base: {kb_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to update knowledge base {kb_id}: {str(e)}")
        raise


@router.delete("/{kb_id}")
def delete_knowledge_base(
    kb_id: UUID,
    db: DbSession,
    current_user: CurrentUser,
):
    """Delete a knowledge base."""
    logger.info(f"Deleting knowledge base: {kb_id}")
    try:
        result = service.delete_kb_entry(db, kb_id, current_user.get_uuid())
        logger.info(f"Successfully deleted knowledge base: {kb_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to delete knowledge base {kb_id}: {str(e)}")
        raise


# Documents endpoints for knowledge bases
@router.get("/{kb_id}/documents", response_model=models.PaginatedDocumentResponse)
def get_knowledge_base_documents(
    kb_id: UUID,
    db: DbSession,
    current_user: CurrentUser,
    offset: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
):
    """Get documents for a specific knowledge base."""
    logger.info(
        f"Getting documents for knowledge base {kb_id} with offset={offset}, limit={limit}"
    )
    try:
        documents, total = service.get_documents_by_knowledge_base(
            db, kb_id, offset, limit
        )
        pages = (total + limit - 1) // limit
        page = (offset // limit) + 1

        # Transform documents to list response format
        document_responses = []
        for doc in documents:
            doc_response = models.DocumentListResponse(
                id=doc.id,
                name=doc.name,
                description=doc.description,
                document_type=doc.document_type,
                created_at=doc.created_at,
                updated_at=doc.updated_at,
                sync_status=doc.sync_status,
                knowledge_base_id=doc.knowledge_base_id,
            )
            document_responses.append(doc_response)

        logger.info(f"Retrieved {len(documents)} documents out of {total} total")
        return {
            "items": document_responses,
            "total": total,
            "page": page,
            "limit": limit,
            "pages": pages,
        }
    except Exception as e:
        logger.error(f"Failed to get documents for knowledge base {kb_id}: {str(e)}")
        raise


@router.get("/{kb_id}/documents/{doc_id}", response_model=models.DocumentResponse)
def get_knowledge_base_document(
    kb_id: UUID, doc_id: UUID, db: DbSession, current_user: CurrentUser
):
    """Get a specific document from a knowledge base."""
    logger.info(f"Getting document {doc_id} from knowledge base {kb_id}")
    try:
        # Get the document and verify it belongs to the knowledge base
        document = service.get_document_by_id(db, doc_id)
        if document.knowledge_base_id != kb_id:
            logger.warning(
                f"Document {doc_id} does not belong to knowledge base {kb_id}"
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found in this knowledge base",
            )

        logger.info(
            f"Successfully retrieved document {doc_id} from knowledge base {kb_id}"
        )
        return document
    except Exception as e:
        logger.error(
            f"Failed to get document {doc_id} from knowledge base {kb_id}: {str(e)}"
        )
        raise


@router.post(
    "/{kb_id}/documents",
    response_model=models.DocumentUploadResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_document(
    db: DbSession,
    current_user: CurrentUser,
    kb_id: UUID,
    file: Optional[UploadFile] = File(None),
    external_url: Optional[str] = Form(None),
    name: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    content: Optional[str] = Form(None),
):
    """Create a document in a knowledge base (file upload, URL, or text content)."""
    logger.info(f"Creating document in knowledge base {kb_id}")

    try:
        if file:
            # File upload
            result = await service.create_document(
                db, file, kb_id, name, description, current_user.get_uuid()
            )

            response = models.DocumentUploadResponse(
                id=result.id,
                name=result.name,
                document_type=(
                    result.document_type.value
                    if hasattr(result.document_type, "value")
                    else result.document_type
                ),
                content_extracted=False,  # Content extraction handled by Celery task
                message="File uploaded successfully. Content extraction and indexing in progress.",
            )

        elif external_url:
            # External URL
            result = await service.create_document_from_url(
                db, external_url, kb_id, name, description, current_user.get_uuid()
            )

            response = models.DocumentUploadResponse(
                id=result.id,
                name=result.name,
                document_type=(
                    result.document_type.value
                    if hasattr(result.document_type, "value")
                    else result.document_type
                ),
                content_extracted=False,  # Content extraction handled by Celery task
                message="URL document created successfully. Content fetching and indexing in progress.",
            )

        elif content:
            # Text content
            result = await service.create_document_from_text(
                db, content, kb_id, name, description, current_user.get_uuid()
            )

            response = models.DocumentUploadResponse(
                id=result.id,
                name=result.name,
                document_type=(
                    result.document_type.value
                    if hasattr(result.document_type, "value")
                    else result.document_type
                ),
                content_extracted=True,
                message="Text document created successfully",
            )

        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Must provide either file, external_url, or content",
            )

        logger.info(f"Successfully created document: {result.id}")
        return response

    except Exception as e:
        logger.error(f"Failed to create document in knowledge base {kb_id}: {str(e)}")
        raise


@router.put("/{kb_id}/documents/{doc_id}", response_model=models.DocumentResponse)
async def update_document(
    db: DbSession,
    current_user: CurrentUser,
    kb_id: UUID,
    doc_id: UUID,
    file: Optional[UploadFile] = File(None),
    external_url: Optional[str] = Form(None),
    name: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    content: Optional[str] = Form(None),
):
    """Update a document (replace file, update URL, or modify content)."""
    logger.info(f"Updating document {doc_id} in knowledge base {kb_id}")

    try:
        # Verify document belongs to the knowledge base
        document = service.get_document_by_id(db, doc_id)
        if document.knowledge_base_id != kb_id:
            logger.warning(
                f"Document {doc_id} does not belong to knowledge base {kb_id}"
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found in this knowledge base",
            )

        if file:
            # File update
            update_data = (
                models.DocumentFileUpdate(name=name, description=description)
                if name or description
                else None
            )
            result = await service.update_document(
                db, doc_id, file, update_data, current_user.get_uuid()
            )
        elif external_url or content or name or description:
            # URL, content, or metadata update
            result = await service.update_document_content(
                db,
                doc_id,
                external_url,
                content,
                name,
                description,
                current_user.get_uuid(),
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Must provide file, external_url, content, or metadata to update",
            )

        logger.info(f"Successfully updated document {doc_id}")
        return result

    except Exception as e:
        logger.error(f"Failed to update document {doc_id}: {str(e)}")
        raise


@router.delete("/{kb_id}/documents/{doc_id}")
async def delete_document(
    kb_id: UUID, doc_id: UUID, db: DbSession, current_user: CurrentUser
):
    """Delete a document from a knowledge base with complete cleanup."""
    logger.info(f"Deleting document {doc_id} from knowledge base {kb_id}")

    try:
        # Verify document belongs to the knowledge base
        document = service.get_document_by_id(db, doc_id)
        if document.knowledge_base_id != kb_id:
            logger.warning(
                f"Document {doc_id} does not belong to knowledge base {kb_id}"
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found in this knowledge base",
            )

        result = await service.delete_document(db, doc_id, current_user.get_uuid())
        logger.info(f"Successfully deleted document {doc_id}")
        return result

    except Exception as e:
        logger.error(f"Failed to delete document {doc_id}: {str(e)}")
        raise


@router.get("/{kb_id}/documents/{doc_id}/download")
async def download_document_file(
    kb_id: UUID, doc_id: UUID, db: DbSession, current_user: CurrentUser
):
    """Download the original file for a document."""
    logger.info(f"Downloading file for document {doc_id} from knowledge base {kb_id}")

    try:
        # Get document and verify it belongs to the knowledge base
        document = service.get_document_by_id(db, doc_id)
        if document.knowledge_base_id != kb_id:
            logger.warning(
                f"Document {doc_id} does not belong to knowledge base {kb_id}"
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found in this knowledge base",
            )

        # Check if document has a file to download
        file_path = None
        if document.meta_data and document.meta_data.get("file_path"):
            file_path = document.meta_data.get("file_path")

        if not file_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No file associated with this document",
            )

        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="File not found on disk"
            )

        # Get original filename from metadata or document name
        original_filename = document.name
        if document.meta_data and document.meta_data.get("original_filename"):
            original_filename = document.meta_data["original_filename"]

        # Get MIME type
        mime_type = "application/octet-stream"
        if document.meta_data and document.meta_data.get("mime_type"):
            mime_type = document.meta_data.get("mime_type")

        logger.info(f"Successfully serving file download for document {doc_id}")
        return FileResponse(
            path=file_path, filename=original_filename, media_type=mime_type
        )

    except Exception as e:
        logger.error(f"Failed to download file for document {doc_id}: {str(e)}")
        raise
