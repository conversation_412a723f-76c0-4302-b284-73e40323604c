from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from entities.incident_report import ApprovalStatusEnum
from pydantic import BaseModel, Field


class ImpactAssessment(BaseModel):
    impact_forecast: Optional[str] = None
    cascading_risks: Optional[str] = None

    class Config:
        from_attributes = True


class IncidentReportResponse(BaseModel):
    incident_id: UUID
    executive_summary: Optional[str] = None
    post_incident_actions: List[Any] = Field(default_factory=list)
    retrospectives: Optional[Dict[str, Any]] = Field(default_factory=dict)
    action_items: List[Dict[str, Any]] = Field(default_factory=list)
    approval_status: ApprovalStatusEnum
    comments: Optional[str] = None
    approved_by: Optional[UUID] = None
    approved_at: Optional[datetime] = None
    root_cause: Optional[str] = None
    immediate_action: Optional[str] = None
    impact_assessment: Optional[ImpactAssessment] = None
    ai_generated: bool = True

    class Config:
        from_attributes = True


class ApprovalStatusCheck(BaseModel):
    approval_status: ApprovalStatusEnum
    comments: Optional[str] = None
    approved_by: Optional[UUID] = None
    approved_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ApprovalRequest(BaseModel):
    approved: bool
    comments: Optional[str] = None

    class Config:
        use_enum_values = True


class RetrospectivesUpdateRequest(BaseModel):
    retrospectives: Optional[Dict[str, Any]] = Field(default_factory=dict)

    class Config:
        from_attributes = True


class ExecutiveSummaryUpdateRequest(BaseModel):
    executive_summary: Optional[str] = None

    class Config:
        from_attributes = True


class RootCauseUpdateRequest(BaseModel):
    root_cause: Optional[str] = None

    class Config:
        from_attributes = True


class ImmediateActionUpdateRequest(BaseModel):
    immediate_action: Optional[str] = None

    class Config:
        from_attributes = True


class ImpactAssessmentUpdateRequest(BaseModel):
    impact_assessment: Optional[ImpactAssessment] = None

    class Config:
        from_attributes = True


class ActionItemsUpdateRequest(BaseModel):
    action_items: List[Dict[str, Any]] = Field(default_factory=list)

    class Config:
        from_attributes = True
