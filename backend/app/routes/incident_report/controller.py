from uuid import UUID

from database.core import DbSession
from db_services import incident_report as incident_report_db_service
from entities.user import User<PERSON><PERSON>
from fastapi import APIRouter, HTTPException
from routes.auth.service import CurrentUser
from routes.incident_report import service

from .models import (
    ActionItemsUpdateRequest,
    ApprovalRequest,
    ApprovalStatusCheck,
    ExecutiveSummaryUpdateRequest,
    ImmediateActionUpdateRequest,
    ImpactAssessmentUpdateRequest,
    IncidentReportResponse,
    RetrospectivesUpdateRequest,
    RootCauseUpdateRequest,
)

router = APIRouter(prefix="/incident/{incident_id}/report", tags=["Incident Report"])


@router.get("", response_model=IncidentReportResponse)
def get_incident_report(incident_id: UUID, db: DbSession, current_user: CurrentUser):
    report = incident_report_db_service.get_incident_report_with_details(
        db, incident_id
    )
    if not report:
        raise HTTPException(status_code=404, detail="Incident report not found")
    return report


@router.get("/status", response_model=ApprovalStatusCheck)
def get_incident_approval_status(
    incident_id: UUID, db: DbSession, current_user: CurrentUser
):
    report = service.get_incident_approval_status(db, incident_id)
    if not report:
        raise HTTPException(status_code=404, detail="Incident report not found")
    return report


@router.post("/approval", response_model=IncidentReportResponse)
def update_incident_report_approval(
    incident_id: UUID,
    approval_request: ApprovalRequest,
    db: DbSession,
    current_user: CurrentUser,
):
    if current_user.get_user_role() != UserRole.ADMIN:
        raise HTTPException(
            status_code=403,
            detail="Only admin users can approve or reject incident reports",
        )

    updated_report = service.update_incident_report_approval(
        db=db,
        incident_id=incident_id,
        approved=approval_request.approved,
        comments=approval_request.comments,
        approved_by_user_id=current_user.get_uuid(),
    )
    return updated_report


@router.patch("/retrospectives", response_model=IncidentReportResponse)
def update_retrospectives(
    incident_id: UUID,
    update_request: RetrospectivesUpdateRequest,
    db: DbSession,
    current_user: CurrentUser,
):
    if current_user.get_user_role() not in [
        UserRole.ENGINEER,
        UserRole.MANAGER,
        UserRole.ADMIN,
    ]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to update incident reports",
        )

    updated_report = service.update_retrospectives(
        db=db,
        incident_id=incident_id,
        retrospectives=update_request.retrospectives,
    )
    return updated_report


@router.patch("/executive-summary", response_model=IncidentReportResponse)
def update_executive_summary(
    incident_id: UUID,
    update_request: ExecutiveSummaryUpdateRequest,
    db: DbSession,
    current_user: CurrentUser,
):
    if current_user.get_user_role() not in [
        UserRole.ENGINEER,
        UserRole.MANAGER,
        UserRole.ADMIN,
    ]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to update incident reports",
        )

    updated_report = service.update_executive_summary(
        db=db,
        incident_id=incident_id,
        executive_summary=update_request.executive_summary,
    )
    return updated_report


@router.patch("/root-cause", response_model=IncidentReportResponse)
def update_root_cause(
    incident_id: UUID,
    update_request: RootCauseUpdateRequest,
    db: DbSession,
    current_user: CurrentUser,
):
    if current_user.get_user_role() not in [
        UserRole.ENGINEER,
        UserRole.MANAGER,
        UserRole.ADMIN,
    ]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to update incident reports",
        )

    updated_report = service.update_root_cause(
        db=db,
        incident_id=incident_id,
        root_cause=update_request.root_cause,
    )
    return updated_report


@router.patch("/immediate-action", response_model=IncidentReportResponse)
def update_immediate_action(
    incident_id: UUID,
    update_request: ImmediateActionUpdateRequest,
    db: DbSession,
    current_user: CurrentUser,
):
    """Update only the immediate action field"""
    if current_user.get_user_role() not in [
        UserRole.ENGINEER,
        UserRole.MANAGER,
        UserRole.ADMIN,
    ]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to update incident reports",
        )

    updated_report = service.update_immediate_action(
        db=db,
        incident_id=incident_id,
        immediate_action=update_request.immediate_action,
    )
    return updated_report


@router.patch("/impact-assessment", response_model=IncidentReportResponse)
def update_impact_assessment(
    incident_id: UUID,
    update_request: ImpactAssessmentUpdateRequest,
    db: DbSession,
    current_user: CurrentUser,
):
    if current_user.get_user_role() not in [
        UserRole.ENGINEER,
        UserRole.MANAGER,
        UserRole.ADMIN,
    ]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to update incident reports",
        )

    impact_forecast = None
    cascading_risks = None
    if update_request.impact_assessment:
        impact_forecast = update_request.impact_assessment.impact_forecast
        cascading_risks = update_request.impact_assessment.cascading_risks

    updated_report = service.update_impact_assessment(
        db=db,
        incident_id=incident_id,
        impact_forecast=impact_forecast,
        cascading_risks=cascading_risks,
    )
    return updated_report


@router.patch("/action-items", response_model=IncidentReportResponse)
def update_action_items(
    incident_id: UUID,
    update_request: ActionItemsUpdateRequest,
    db: DbSession,
    current_user: CurrentUser,
):
    if current_user.get_user_role() not in [
        UserRole.ENGINEER,
        UserRole.MANAGER,
        UserRole.ADMIN,
    ]:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions to update incident reports",
        )

    updated_report = service.update_action_items(
        db=db,
        incident_id=incident_id,
        action_items=update_request.action_items,
    )
    return updated_report
