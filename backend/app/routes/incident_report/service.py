from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import UUID

from database.core import DbSession
from db_services import incident as incident_db_service
from db_services import incident_report as incident_report_db_service
from entities.incident import IncidentDetail
from entities.incident_report import (
    ApprovalStatusEnum,
    IncidentReport,
)
from fastapi import HTTPException
from utils.logger import get_service_logger

logger = get_service_logger("incident_report")


def get_incident_approval_status(
    db: DbSession, incident_id: UUID
) -> Optional[IncidentReport]:
    return incident_report_db_service.get_incident_report(db, incident_id)


def update_incident_report_approval(
    db: DbSession,
    incident_id: UUID,
    approved: bool,
    comments: Optional[str] = None,
    approved_by_user_id: Optional[UUID] = None,
) -> Dict[str, Any]:
    report = incident_report_db_service.get_incident_report(db, incident_id)
    if not report:
        raise HTTPException(status_code=404, detail="Incident report not found")

    if approved:
        report.approval_status = ApprovalStatusEnum.APPROVED
        try:
            from tasks.vector_db import upsert_incident_report_embedding_task

            task = upsert_incident_report_embedding_task.delay(str(incident_id))
            logger.info(
                f"Triggered async embedding task {task.id} for incident report{incident_id}"
            )
        except Exception as e:
            logger.warning(
                f"Failed to trigger embedding task for incident report{incident_id}: {e}"
            )

    else:
        report.approval_status = ApprovalStatusEnum.REJECTED

    report.comments = comments
    report.approved_by = approved_by_user_id
    report.approved_at = datetime.now(timezone.utc)

    try:
        db.commit()
        db.refresh(report)
        return incident_report_db_service.get_incident_report_with_details(
            db, incident_id
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to update report approval: {str(e)}"
        )


def update_retrospectives(
    db: DbSession, incident_id: UUID, retrospectives: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    report = incident_report_db_service.get_incident_report(db, incident_id)
    if not report:
        raise HTTPException(status_code=404, detail="Incident report not found")

    report.retrospectives = retrospectives
    report.ai_generated = False

    try:
        db.commit()
        db.refresh(report)
        return incident_report_db_service.get_incident_report_with_details(
            db, incident_id
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to update retrospectives: {str(e)}"
        )


def update_executive_summary(
    db: DbSession, incident_id: UUID, executive_summary: Optional[str]
) -> Dict[str, Any]:
    report = incident_report_db_service.get_incident_report(db, incident_id)
    if not report:
        raise HTTPException(status_code=404, detail="Incident report not found")

    report.executive_summary = executive_summary
    report.ai_generated = False

    try:
        db.commit()
        db.refresh(report)
        return incident_report_db_service.get_incident_report_with_details(
            db, incident_id
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to update executive summary: {str(e)}"
        )


def update_root_cause(
    db: DbSession, incident_id: UUID, root_cause: Optional[str]
) -> Dict[str, Any]:
    incident_detail = incident_db_service.get_incident_details(db, incident_id)
    report = incident_report_db_service.get_incident_report(db, incident_id)
    if not incident_detail:
        incident_detail = IncidentDetail(incident_id=incident_id)
        db.add(incident_detail)

    incident_detail.root_cause = root_cause
    report.ai_generated = False

    try:
        db.commit()
        db.refresh(incident_detail)
        return incident_report_db_service.get_incident_report_with_details(
            db, incident_id
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to update root cause: {str(e)}"
        )


def update_immediate_action(
    db: DbSession, incident_id: UUID, immediate_action: Optional[str]
) -> Dict[str, Any]:
    incident_detail = incident_db_service.get_incident_details(db, incident_id)
    report = incident_report_db_service.get_incident_report(db, incident_id)
    if not incident_detail:
        incident_detail = IncidentDetail(incident_id=incident_id)
        db.add(incident_detail)

    incident_detail.immediate_action = immediate_action
    report.ai_generated = False

    try:
        db.commit()
        db.refresh(incident_detail)
        return incident_report_db_service.get_incident_report_with_details(
            db, incident_id
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to update immediate action: {str(e)}"
        )


def update_impact_assessment(
    db: DbSession,
    incident_id: UUID,
    impact_forecast: Optional[str],
    cascading_risks: Optional[str],
) -> Dict[str, Any]:
    incident_detail = incident_db_service.get_incident_details(db, incident_id)

    if not incident_detail:
        incident_detail = IncidentDetail(incident_id=incident_id)
        db.add(incident_detail)

    report = incident_report_db_service.get_incident_report(db, incident_id)
    report.ai_generated = False

    incident_detail.impact_forecast = impact_forecast
    incident_detail.cascading_risks = cascading_risks

    try:
        db.commit()
        db.refresh(incident_detail)
        return incident_report_db_service.get_incident_report_with_details(
            db, incident_id
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to update impact assessment: {str(e)}"
        )


def update_action_items(
    db: DbSession, incident_id: UUID, action_items: List[Dict[str, Any]]
) -> Dict[str, Any]:
    report = incident_report_db_service.get_incident_report(db, incident_id)
    if not report:
        raise HTTPException(status_code=404, detail="Incident report not found")

    report.action_items = action_items
    report.ai_generated = False

    try:
        db.commit()
        db.refresh(report)
        return incident_report_db_service.get_incident_report_with_details(
            db, incident_id
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to update action items: {str(e)}"
        )
