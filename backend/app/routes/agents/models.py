from typing import Any, Optional

import pydantic
from google.genai import types
from pydantic import alias_generators


class BaseModel(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(
        alias_generator=alias_generators.to_camel,
        populate_by_name=True,
    )


class AgentRunRequest(BaseModel):
    app_name: str
    agent_name: str
    user_id: str
    session_id: str
    new_message: types.Content
    streaming: bool = False
    state_delta: Optional[dict[str, Any]] = None
