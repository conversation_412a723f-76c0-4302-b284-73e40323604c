import asyncio
from enum import Enum
from typing import Any, List, Literal, Optional
from uuid import UUID

from agents.agent_runner import artifact_service, get_runner, get_session_service
from database.core import DbSession
from db_services import incident as incident_db_service
from db_services import runbooks as runbook_db_service
from fastapi import APIRouter, HTTPException, Query, Response, status
from fastapi.responses import StreamingResponse
from fastapi.websockets import WebSocket, WebSocketDisconnect
from google.adk.agents.live_request_queue import LiveRequest, LiveRequestQueue
from google.adk.agents.run_config import RunConfig, StreamingMode
from google.adk.events import Event
from google.adk.runners import Runner
from google.adk.sessions import Session
from google.genai import types
from pydantic import ValidationError
from utils.logger import get_controller_logger

from routes.agents import service
from routes.agents.models import AgentRunRequest
from routes.auth.service import CurrentUser

logger = get_controller_logger("agents")
router = APIRouter(prefix="/agents", tags=["Agents - [Testing]"])


@router.get("/list-agents")
def list_agents() -> list[str]:
    return service.list_agents()


@router.get(
    "/apps/{app_name}/users/{user_id}/sessions",
    response_model_exclude_none=True,
)
async def list_sessions(app_name: str, user_id: str) -> list[Session]:
    list_sessions_response = await get_session_service().list_sessions(
        app_name=app_name, user_id=user_id
    )
    return list_sessions_response.sessions


@router.get(
    "/apps/{app_name}/users/{user_id}/sessions/{session_id}",
    response_model_exclude_none=True,
)
async def get_session(
    app_name: str,
    user_id: str,
    session_id: str,
) -> Session:
    session = await get_session_service().get_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    logger.info("Retrieved session: %s", session_id)
    return session


@router.post(
    "/apps/{app_name}/users/{user_id}/sessions/{session_id}",
    response_model_exclude_none=True,
)
async def create_session_with_id(
    app_name: str,
    user_id: str,
    session_id: str,
    state: Optional[dict[str, Any]] = None,
) -> Session:
    if (
        await get_session_service().get_session(
            app_name=app_name, user_id=user_id, session_id=session_id
        )
        is not None
    ):
        raise HTTPException(
            status_code=400, detail=f"Session already exists: {session_id}"
        )
    session = await get_session_service().create_session(
        app_name=app_name, user_id=user_id, state=state, session_id=session_id
    )
    logger.info("New session created: %s", session_id)
    return session


@router.post(
    "/apps/{app_name}/users/{user_id}/sessions",
    response_model_exclude_none=True,
)
async def create_session(
    app_name: str,
    user_id: str,
    state: Optional[dict[str, Any]] = None,
    events: Optional[list[Event]] = None,
) -> Session:
    session = await get_session_service().create_session(
        app_name=app_name, user_id=user_id, state=state
    )

    if events:
        for event in events:
            await get_session_service().append_event(session=session, event=event)

    logger.info("New session created")
    return session


@router.delete("/apps/{app_name}/users/{user_id}/sessions/{session_id}")
async def delete_session(app_name: str, user_id: str, session_id: str):
    await get_session_service().delete_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )


@router.get(
    "/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts/{artifact_name}",
    response_model_exclude_none=True,
)
async def load_artifact(
    app_name: str,
    user_id: str,
    session_id: str,
    artifact_name: str,
    version: Optional[int] = Query(None),
) -> Optional[types.Part]:
    artifact = await artifact_service.load_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=artifact_name,
        version=version,
    )
    if not artifact:
        raise HTTPException(status_code=404, detail="Artifact not found")
    return artifact


@router.get(
    "/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts/{artifact_name}/versions/{version_id}",
    response_model_exclude_none=True,
)
async def load_artifact_version(
    app_name: str,
    user_id: str,
    session_id: str,
    artifact_name: str,
    version_id: int,
) -> Optional[types.Part]:
    artifact = await artifact_service.load_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=artifact_name,
        version=version_id,
    )
    if not artifact:
        raise HTTPException(status_code=404, detail="Artifact not found")
    return artifact


@router.get(
    "/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts",
    response_model_exclude_none=True,
)
async def list_artifact_names(
    app_name: str, user_id: str, session_id: str
) -> list[str]:
    return await artifact_service.list_artifact_keys(
        app_name=app_name, user_id=user_id, session_id=session_id
    )


@router.get(
    "/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts/{artifact_name}/versions",
    response_model_exclude_none=True,
)
async def list_artifact_versions(
    app_name: str, user_id: str, session_id: str, artifact_name: str
) -> list[int]:
    return await artifact_service.list_versions(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=artifact_name,
    )


@router.delete(
    "/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts/{artifact_name}",
)
async def delete_artifact(
    app_name: str, user_id: str, session_id: str, artifact_name: str
):
    await artifact_service.delete_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=artifact_name,
    )


@router.post("/run", response_model_exclude_none=True)
async def agent_run(req: AgentRunRequest) -> list[Event]:
    session = await get_session_service().get_session(
        app_name=req.app_name, user_id=req.user_id, session_id=req.session_id
    )
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    runner = get_runner(app_name=req.app_name, agent=service.get_agent(req.agent_name))
    events = [
        event
        async for event in runner.run_async(
            user_id=req.user_id,
            session_id=req.session_id,
            new_message=req.new_message,
        )
    ]
    logger.info("Generated %s events in agent run", len(events))
    logger.debug("Events generated: %s", events)
    return events


@router.post("/run_sse")
async def agent_run_sse(req: AgentRunRequest) -> StreamingResponse:
    # SSE endpoint
    session = await get_session_service().get_session(
        app_name=req.app_name, user_id=req.user_id, session_id=req.session_id
    )
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # Convert the events to properly formatted SSE
    async def event_generator():
        try:
            stream_mode = StreamingMode.SSE if req.streaming else StreamingMode.NONE
            runner = get_runner(
                app_name=req.app_name, agent=service.get_agent(req.agent_name)
            )
            async for event in runner.run_async(
                user_id=req.user_id,
                session_id=req.session_id,
                new_message=req.new_message,
                state_delta=req.state_delta,
                run_config=RunConfig(streaming_mode=stream_mode),
            ):
                # Format as SSE data
                sse_event = event.model_dump_json(exclude_none=True, by_alias=True)
                logger.debug("Generated event in agent run streaming: %s", sse_event)
                yield f"data: {sse_event}\n\n"
        except Exception as e:
            logger.exception("Error in event_generator: %s", e)
            # You might want to yield an error event here
            yield f'data: {{"error": "{str(e)}"}}\n\n'

    # Returns a streaming response with the proper media type for SSE
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
    )


@router.websocket("/run_live")
async def agent_live_run(
    websocket: WebSocket,
    app_name: str,
    agent_name: str,
    user_id: str,
    session_id: str,
    modalities: List[Literal["TEXT", "AUDIO"]] = Query(
        default=["TEXT", "AUDIO"]
    ),  # Only allows "TEXT" or "AUDIO"
) -> None:
    await websocket.accept()

    session = await get_session_service().get_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    if not session:
        # Accept first so that the client is aware of connection establishment, then close with a specific code.
        await websocket.close(code=1002, reason="Session not found")
        return

    live_request_queue = LiveRequestQueue()

    async def forward_events():
        runner: Runner = get_runner(
            app_name=app_name, agent=service.get_agent(agent_name)
        )
        async for event in runner.run_live(
            session=session, live_request_queue=live_request_queue
        ):
            await websocket.send_text(
                event.model_dump_json(exclude_none=True, by_alias=True)
            )

    async def process_messages():
        try:
            while True:
                data = await websocket.receive_text()
                # Validate and send the received message to the live queue.
                live_request_queue.send(LiveRequest.model_validate_json(data))
        except ValidationError as ve:
            logger.error("Validation error in process_messages: %s", ve)

    # Run both tasks concurrently and cancel all if one fails.
    tasks = [
        asyncio.create_task(forward_events()),
        asyncio.create_task(process_messages()),
    ]
    done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_EXCEPTION)
    try:
        # This will re-raise any exception from the completed tasks.
        for task in done:
            task.result()
    except WebSocketDisconnect:
        logger.info("Client disconnected during process_messages.")
    except Exception as e:
        logger.exception("Error during live websocket communication: %s", e)
        WEBSOCKET_INTERNAL_ERROR_CODE = 1011
        WEBSOCKET_MAX_BYTES_FOR_REASON = 123
        await websocket.close(
            code=WEBSOCKET_INTERNAL_ERROR_CODE,
            reason=str(e)[:WEBSOCKET_MAX_BYTES_FOR_REASON],
        )
    finally:
        for task in pending:
            task.cancel()


@router.post("/query")
async def query_agent(db: DbSession, current_user: CurrentUser, query: str):
    """
    Query the coordinator agent with a raw query.
    """
    logger.info(
        f"Processing general query from user {current_user.get_uuid()}: {query[:100]}..."
    )
    try:
        result = await service.handle_general_query(db, current_user, query)
        logger.info(
            f"Successfully processed general query for user {current_user.get_uuid()}"
        )
        return result
    except Exception as e:
        logger.error(f"Failed to process general query: {str(e)}")
        raise


@router.post("/{incident_id}/root_cause_agent", response_model=dict)
async def analyze_incident_root_cause(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
):
    """
    Analyze the root cause of an incident by directly invoking the root cause analyzer agent.
    """
    logger.info(
        f"Starting root cause analysis for incident {incident_id} by user {current_user.get_uuid()}"
    )
    incident = incident_db_service.get_incident_by_id(db, incident_id)
    if not incident:
        logger.warning(f"Incident not found: {incident_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Incident not found"
        )

    try:
        result = await service.handle_root_cause_agent_request(
            db, current_user, incident
        )
        logger.info(
            f"Successfully completed root cause analysis for incident {incident_id}"
        )
        return result
    except Exception as e:
        logger.error(
            f"Failed to analyze root cause for incident {incident_id}: {str(e)}"
        )
        raise


@router.post("/{incident_id}/runbook_agent")
async def generate_runbook_steps(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
    runbook_id: Optional[UUID] = None,
    runbook_type: Optional[str] = None,
):
    """
    Generate actionable solutions for an incident by directly invoking the runbook generator agent.
    """
    logger.info(
        f"Generating runbook steps for incident {incident_id} by user {current_user.get_uuid()}"
    )

    if not (runbook_id or runbook_type):
        logger.warning(f"Missing runbook_id or runbook_type for incident {incident_id}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Must specify either runbook_id or runbook_type",
        )

    try:
        incident = incident_db_service.get_incident_by_id(db, incident_id)
        if not incident:
            logger.warning(f"Incident not found: {incident_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Incident not found"
            )
        existing_steps = []
        if runbook_id:
            logger.info(
                f"Using existing runbook {runbook_id} for incident {incident_id}"
            )
            runbook = runbook_db_service.get_runbook_by_id(db, runbook_id)
            if not runbook:
                logger.warning(f"Runbook not found: {runbook_id}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Runbook not found"
                )

            runbook_type = runbook.type  # Override if runbook is provided
            runbook_steps = runbook_db_service.get_steps_by_runbook(db, runbook_id)
            existing_steps = [
                {
                    "title": step.title,
                    "description": step.description,
                    "details": step.details,
                    "expected_result": step.expected_result,
                    "status": (
                        step.status.value
                        if isinstance(step.status, Enum)
                        else step.status
                    ),
                    "notes": step.notes,
                }
                for step in runbook_steps
            ]
            logger.info(
                f"Found {len(existing_steps)} existing steps for runbook {runbook_id}"
            )

        if not runbook_type:
            logger.warning(f"Missing runbook_type for incident {incident_id}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Must specify runbook_type",
            )

        logger.info(
            f"Invoking runbook agent for incident {incident_id} with type {runbook_type}"
        )
        result = await service.handle_runbook_agent_request(
            db, current_user, incident, runbook_type, existing_steps
        )
        logger.info(f"Successfully generated runbook steps for incident {incident_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to generate runbook steps for incident {incident_id}: {str(e)}"
        )
        raise


@router.post("/{incident_id}/reporter_agent")
async def generate_incident_report(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
):
    """
    Generate a detailed incident report by directly invoking the reporter agent.
    """
    if not incident_id:
        raise HTTPException(status_code=400, detail="Must specify incident_id")
    incident = incident_db_service.get_incident_by_id(db, incident_id)
    if not incident:
        raise HTTPException(status_code=404, detail="Incident not found")
    md_content = await service.handle_reporter_agent_request(
        db, current_user, incident_id
    )
    return Response(
        content=md_content,
        media_type="text/markdown",
        headers={
            "Content-Disposition": f'attachment; filename="incident_{incident_id}.md"'
        },
    )


@router.post("/log_analytics_agent")
async def log_analytics_agent(
    db: DbSession, current_user: CurrentUser, instruction: str
):
    return await service.handle_log_analytics_agent_request(
        db, current_user, instruction
    )


@router.get("/{incident_id}/incident_manager_agent")
async def incident_manager_agent(
    db: DbSession, current_user: CurrentUser, incident_id: UUID, query: str
):
    return await service.handle_incident_manager_agent_request(
        db, current_user, incident_id, query
    )
