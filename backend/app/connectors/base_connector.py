from abc import ABC, abstractmethod
from contextlib import contextmanager
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Literal, Optional
from uuid import UUID

import instructor
import litellm
from database.core import SessionLocal
from db_services import incident as incident_db_service
from entities.incident import Incident, IncidentDetail
from pydantic import BaseModel
from routes.incidents.models import IncidentCreate, IncidentUpdate
from utils.logger import get_service_logger

logger = get_service_logger("base_connector")


class IncidentDetailsAIResponse(BaseModel):
    """Pydantic model for AI-generated incident details."""

    summary: str
    severity: Literal["low", "medium", "high", "critical"]
    incident_type: Literal[
        "outage",
        "degradation",
        "security",
        "performance",
        "other",
    ]
    problem_description: str
    expected_resolution: str


class SaveType(Enum):
    """Enum for save operation types"""

    ADD = "Add"
    UPDATE = "Update"


class ConnectorError(Exception):
    """Base exception for connector operations"""

    pass


class DatabaseError(ConnectorError):
    """Exception for database operations"""

    pass


class RateLimitError(ConnectorError):
    """Exception for rate limit issues"""

    pass


@contextmanager
def db_session():
    """Context manager for database sessions with proper cleanup"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


class BaseConnector(ABC):
    """Abstract base class for all issue tracking system connectors"""

    # Configuration constants
    DEFAULT_MAX_RETRIES = 3
    DEFAULT_LLM_MODEL = "gemini/gemini-2.5-flash-lite-preview-06-17"

    def __init__(self, config: Dict[str, Any]):
        """Initialize with configuration dictionary"""
        self.config = config
        super().__init__()
        self.progress_callback: Any = None

    def register_callback(self, callback: Callable[[int, int, int, str], None]) -> None:
        """Register a callback function for progress updates"""
        self.progress_callback = callback

    def get_incident_number(self, prefix: str, issue_id: str) -> str:
        """Generate a unique incident number based on issue ID"""
        return f"{prefix}-{issue_id}"

    def _generate_incident_details_with_ai(
        self, title: str, content: str, source_type: str = "issue"
    ) -> IncidentDetailsAIResponse | None:
        """Generate comprehensive incident details using AI"""

        try:
            litellm.enable_json_schema_validation = True
            prompt = f"""
                Analyze the following {source_type} and provide a summary in JSON format.
                The JSON object should include the following keys:
                "summary": Provide a concise summary of the {source_type} in 50 words or less.
                "severity": Provide a severity level for the incident. The severity levels are low, medium, high, critical.
                "incident_type": Classify the incident type. The types are outage, degradation, security, performance, other.
                "problem_description": Summarize the problem described in the {source_type} in 100 words or less.
                "expected_resolution": Describe the ideal resolution for the issue in 100 words or less.

                Title: {title}
                Content: {content}
            """
            client = instructor.from_litellm(litellm.completion)
            response = client.chat.completions.create(
                model=self.DEFAULT_LLM_MODEL,
                messages=[{"role": "user", "content": prompt}],
                response_model=IncidentDetailsAIResponse,
            )
            return response

        except Exception as e:
            logger.error(f"AI details generation failed: {e}")
            return None

    @abstractmethod
    def read_project_info(self) -> Dict[str, Any]:
        """Fetch project metadata like README"""
        pass

    @abstractmethod
    def read_open_issues(
        self, since: Optional[datetime] = None, until: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """Fetch open issues with optional date filters"""
        pass

    @abstractmethod
    def read_closed_issues(
        self, since: datetime, until: datetime
    ) -> List[Dict[str, Any]]:
        """Fetch closed issues in date range"""
        pass

    @abstractmethod
    def post_comment(self, issue_id: int, comment: str) -> bool:
        """Post comment to an issue"""
        pass

    def add_to_db(self, incident_create: IncidentCreate) -> bool:
        """Add incident to database using database service directly"""
        try:
            with db_session() as db:
                incident = Incident(
                    incident_number=incident_create.incident_number,
                    title=incident_create.title,
                    summary=incident_create.summary,
                    priority=incident_create.priority,
                    severity=incident_create.severity,
                    incident_type=incident_create.incident_type,
                    status=incident_create.status,
                    reported_at=incident_create.reported_at,
                )
                db.add(incident)
                db.flush()

                # Create the incident details
                detail = IncidentDetail(
                    incident_id=incident.id,
                    affected_services=incident_create.affected_services or [],
                    tags=incident_create.tags or [],
                    incident_details=incident_create.incident_details,
                    attachments=incident_create.attachments or [],
                )
                db.add(detail)
                db.commit()

                logger.info(
                    f"Successfully created incident {incident.incident_number} with status {incident.status}"
                )
                return True
        except Exception as e:
            logger.error(f"Failed to add incident '{incident_create.title}': {e}")
            raise DatabaseError(f"Failed to add incident: {e}") from e

    def update_db(self, incident_id: str, incident_update: IncidentUpdate) -> bool:
        """Update incident in database using database service directly"""
        try:
            with db_session() as db:
                incident = incident_db_service.get_incident_by_id(db, UUID(incident_id))
                if not incident:
                    logger.warning(f"Incident not found for update: {incident_id}")
                    return False

                # Update incident fields
                update_data = incident_update.model_dump(exclude_unset=True)

                for field in [
                    "title",
                    "summary",
                    "priority",
                    "severity",
                    "incident_type",
                    "status",
                ]:
                    if field in update_data:
                        setattr(incident, field, update_data[field])

                # Update or create incident details if detail fields are provided
                detail_fields = [
                    "affected_services",
                    "tags",
                    "incident_details",
                    "attachments",
                ]
                if any(field in update_data for field in detail_fields):
                    detail = incident_db_service.get_incident_details(db, incident.id)
                    if not detail:
                        detail = IncidentDetail(incident_id=incident.id)
                        db.add(detail)

                    # Update detail fields
                    for field in detail_fields:
                        if field in update_data:
                            setattr(detail, field, update_data[field])

                db.commit()
                logger.info(f"Successfully updated incident {incident.incident_number}")
                return True

        except Exception as e:
            logger.error(f"Failed to update incident '{incident_id}': {e}")
            raise DatabaseError(f"Failed to update incident: {e}") from e

    def fetch_existing_incident(self, incident_number: str) -> Incident | None:
        """Fetch existing incident using database service directly"""
        try:
            with db_session() as db:
                incident = incident_db_service.get_incident_by_number(
                    db, incident_number
                )
                return incident
        except Exception as e:
            logger.error(
                f"Failed to fetch incident with incident_number {incident_number}: {e}"
            )
            raise DatabaseError(f"Database operation failed: {e}") from e

    def save_incidents(
        self,
        incidents: List[Dict[str, Any]],
        save_type: SaveType,
        updated_incident_ids: Optional[List[str]] = None,
    ) -> int:
        """Save multiple incidents to database"""
        if not incidents:
            return 0

        success_count = 0
        total_count = len(incidents)

        if save_type == SaveType.UPDATE and not updated_incident_ids:
            raise ConnectorError("updated_incident_ids required for UPDATE operations")

        for idx, incident_data in enumerate(incidents):
            try:
                if save_type == SaveType.ADD:
                    incident_create = IncidentCreate(**incident_data)
                    result = self.add_to_db(incident_create)
                    if result:
                        success_count += 1

                elif save_type == SaveType.UPDATE and updated_incident_ids:
                    if idx < len(updated_incident_ids):
                        incident_id = updated_incident_ids[idx]
                        update_fields = {
                            k: v
                            for k, v in incident_data.items()
                            if k in IncidentUpdate.model_fields
                        }
                        incident_update = IncidentUpdate(**update_fields)
                        result = self.update_db(incident_id, incident_update)
                        if result:
                            success_count += 1
            except Exception as e:
                logger.error(
                    f"Error processing incident '{incident_data.get('title')}': {e}"
                )
                continue
            finally:
                if self.progress_callback:
                    self.progress_callback(
                        current=idx + 1,
                        total=total_count,
                        success_count=success_count,
                        message=f"{save_type.value} incidents: {success_count} / {total_count} processed",
                    )

        logger.info(
            f"Save operation completed: {success_count}/{total_count} successful"
        )
        return success_count

    @abstractmethod
    def incident_field_matching(
        self,
        issues_data: List[Dict[str, Any]],
        issue_type: str = "closed",
    ) -> List[Dict[str, Any]]:
        """Map issues data to incident format."""
        pass

    @abstractmethod
    def sync_issues(
        self, start_time: datetime, end_time: datetime, incident_type: str = "both"
    ) -> List[Dict]:
        """Sync issues from the source and return updated incident data."""
        pass

    @abstractmethod
    def import_issues(
        self, start_time: datetime, end_time: datetime, incident_type: str
    ) -> List[Dict]:
        """Import issues based on type and save them to the DB."""
        pass
