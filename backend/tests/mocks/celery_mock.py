"""
Celery Mock Implementation
=========================

Provides mock implementations for Celery tasks and Redis backend
to eliminate external dependencies during testing.
"""

from typing import Any, Callable, Dict, Optional
from unittest.mock import MagicMock
from uuid import uuid4


class MockAsyncResult:
    """Mock implementation of Celery AsyncResult."""

    def __init__(self, task_id: str, result: Any = None, state: str = "SUCCESS"):
        self.id = task_id
        self._result = result
        self._state = state
        self.successful = lambda: state == "SUCCESS"
        self.failed = lambda: state == "FAILURE"
        self.ready = lambda: state in ["SUCCESS", "FAILURE"]

    @property
    def result(self):
        return self._result

    @property
    def state(self):
        return self._state

    def get(self, timeout=None, propagate=True):
        """Mock get method that returns the result immediately."""
        if self._state == "FAILURE" and propagate:
            raise Exception("Mock task failed")
        return self._result


class MockTask:
    """Mock implementation of Celery Task."""

    def __init__(self, func: Callable, name: str = None):
        self.func = func
        self.name = name or f"{func.__module__}.{func.__name__}"
        self.request = MagicMock()
        self.retry = MagicMock()

    def delay(self, *args, **kwargs):
        """Mock delay method that executes immediately in tests."""
        task_id = str(uuid4())
        try:
            result = self.func(*args, **kwargs)
            return MockAsyncResult(task_id, result, "SUCCESS")
        except Exception as e:
            return MockAsyncResult(task_id, str(e), "FAILURE")

    def apply_async(self, args=None, kwargs=None, **options):
        """Mock apply_async method."""
        args = args or []
        kwargs = kwargs or {}
        return self.delay(*args, **kwargs)

    def apply(self, args=None, kwargs=None, **options):
        """Mock apply method for synchronous execution."""
        args = args or []
        kwargs = kwargs or {}
        return self.delay(*args, **kwargs)

    def __call__(self, *args, **kwargs):
        """Allow direct calling of the task."""
        return self.func(*args, **kwargs)


class MockCeleryApp:
    """Mock implementation of Celery application."""

    def __init__(self, name: str = "test_app"):
        self.name = name
        self.conf = MagicMock()
        self.tasks = {}
        self._task_registry = {}

        # Configure for eager execution (immediate execution in tests)
        self.conf.task_always_eager = True
        self.conf.task_eager_propagates = True

    def delay(self, *args, **kwargs):
        """Mock delay method for direct task execution."""
        task_id = str(uuid4())
        return MockAsyncResult(task_id, {"status": "completed"}, "SUCCESS")

    def task(self, bind=False, **options):
        """Mock task decorator."""

        def decorator(func):
            task_name = options.get("name", f"{func.__module__}.{func.__name__}")
            mock_task = MockTask(func, task_name)
            self.tasks[task_name] = mock_task
            self._task_registry[task_name] = mock_task

            # Return the mock task, not the original function
            return mock_task

        # If called with a function directly (no parentheses), apply decorator immediately
        if callable(bind) and not isinstance(bind, bool):
            func = bind
            return decorator(func)

        return decorator

    def send_task(self, name: str, args=None, kwargs=None, **options):
        """Mock send_task method."""
        args = args or []
        kwargs = kwargs or {}

        if name in self.tasks:
            return self.tasks[name].delay(*args, **kwargs)
        else:
            # Return a mock result for unknown tasks
            task_id = str(uuid4())
            return MockAsyncResult(task_id, None, "SUCCESS")

    def control_inspect(self):
        """Mock control inspect."""
        return MagicMock()


def mock_celery_task(func: Callable = None, **task_options):
    """
    Decorator to create mock Celery tasks for testing.

    Usage:
        @mock_celery_task
        def my_task(arg1, arg2):
            return arg1 + arg2
    """

    def decorator(f):
        return MockTask(f, task_options.get("name"))

    if func is None:
        return decorator
    else:
        return decorator(func)


# Mock instances for common use cases
mock_celery_app = MockCeleryApp("test_worker")


class MockCeleryTaskResult:
    """Mock result for Celery task execution."""

    def __init__(self, task_id: str, status: str = "SUCCESS", result: Any = None):
        self.task_id = task_id
        self.status = status
        self.result = result
        self.successful = status == "SUCCESS"
        self.failed = status == "FAILURE"
        self.ready = status in ["SUCCESS", "FAILURE", "REVOKED"]

    def get(self, timeout=None, propagate=True):
        """Get the task result."""
        if self.failed and propagate:
            raise Exception(f"Task {self.task_id} failed: {self.result}")
        return self.result

    def forget(self):
        """Mock forget method."""
        pass


# Mock task functions for common operations
def mock_vector_db_task(document_id: str, operation: str = "create") -> Dict[str, Any]:
    """Mock vector database task."""
    return {
        "task_id": str(uuid4()),
        "document_id": document_id,
        "operation": operation,
        "status": "completed",
        "embedding_created": True,
    }


def mock_github_sync_task(start_time: str, end_time: str) -> Dict[str, Any]:
    """Mock GitHub sync task."""
    return {
        "task_id": str(uuid4()),
        "start_time": start_time,
        "end_time": end_time,
        "synced_issues": 5,
        "status": "completed",
    }


def mock_jira_sync_task(project_key: str) -> Dict[str, Any]:
    """Mock Jira sync task."""
    return {
        "task_id": str(uuid4()),
        "project_key": project_key,
        "synced_issues": 3,
        "status": "completed",
    }


# Registry of mock tasks
MOCK_TASK_REGISTRY = {
    "tasks.vector_db.process_document_task": mock_vector_db_task,
    "tasks.github.sync_issues_task": mock_github_sync_task,
    "tasks.jira.sync_issues_task": mock_jira_sync_task,
}


class MockRedisBackend:
    """Mock Redis backend for Celery."""

    def __init__(self):
        self._results = {}

    def store_result(self, task_id: str, result: Any, state: str):
        """Store task result."""
        self._results[task_id] = {"result": result, "state": state}

    def get_result(self, task_id: str):
        """Get task result."""
        return self._results.get(task_id, {"result": None, "state": "PENDING"})

    def delete_result(self, task_id: str):
        """Delete task result."""
        self._results.pop(task_id, None)
