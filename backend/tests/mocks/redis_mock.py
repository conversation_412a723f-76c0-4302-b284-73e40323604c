"""
Redis Mock Implementation
========================

Provides mock implementations for Redis operations
to eliminate external dependencies during testing.
"""

from typing import Any, Dict, List, Optional, Union
from unittest.mock import MagicMock


class MockRedis:
    """Mock implementation of Redis client."""

    def __init__(self, **kwargs):
        self._data = {}
        self._expires = {}
        self._lists = {}
        self._sets = {}
        self._hashes = {}

    def get(self, key: str) -> Optional[bytes]:
        """Mock GET operation."""
        if key in self._data:
            return (
                self._data[key].encode()
                if isinstance(self._data[key], str)
                else self._data[key]
            )
        return None

    def set(self, key: str, value: Union[str, bytes], ex: Optional[int] = None) -> bool:
        """Mock SET operation."""
        self._data[key] = value
        if ex:
            self._expires[key] = ex
        return True

    def delete(self, *keys: str) -> int:
        """Mock DELETE operation."""
        deleted = 0
        for key in keys:
            if key in self._data:
                del self._data[key]
                deleted += 1
            if key in self._expires:
                del self._expires[key]
        return deleted

    def exists(self, key: str) -> bool:
        """Mock EXISTS operation."""
        return key in self._data

    def expire(self, key: str, seconds: int) -> bool:
        """Mock EXPIRE operation."""
        if key in self._data:
            self._expires[key] = seconds
            return True
        return False

    def ttl(self, key: str) -> int:
        """Mock TTL operation."""
        return self._expires.get(key, -1)

    def flushdb(self) -> bool:
        """Mock FLUSHDB operation."""
        self._data.clear()
        self._expires.clear()
        self._lists.clear()
        self._sets.clear()
        self._hashes.clear()
        return True

    def ping(self) -> bool:
        """Mock PING operation."""
        return True

    # List operations
    def lpush(self, key: str, *values: Any) -> int:
        """Mock LPUSH operation."""
        if key not in self._lists:
            self._lists[key] = []
        for value in reversed(values):
            self._lists[key].insert(0, value)
        return len(self._lists[key])

    def rpush(self, key: str, *values: Any) -> int:
        """Mock RPUSH operation."""
        if key not in self._lists:
            self._lists[key] = []
        self._lists[key].extend(values)
        return len(self._lists[key])

    def lpop(self, key: str) -> Optional[Any]:
        """Mock LPOP operation."""
        if key in self._lists and self._lists[key]:
            return self._lists[key].pop(0)
        return None

    def rpop(self, key: str) -> Optional[Any]:
        """Mock RPOP operation."""
        if key in self._lists and self._lists[key]:
            return self._lists[key].pop()
        return None

    def llen(self, key: str) -> int:
        """Mock LLEN operation."""
        return len(self._lists.get(key, []))

    def lrange(self, key: str, start: int, end: int) -> List[Any]:
        """Mock LRANGE operation."""
        if key not in self._lists:
            return []
        return self._lists[key][start : end + 1 if end != -1 else None]

    # Set operations
    def sadd(self, key: str, *values: Any) -> int:
        """Mock SADD operation."""
        if key not in self._sets:
            self._sets[key] = set()
        added = 0
        for value in values:
            if value not in self._sets[key]:
                self._sets[key].add(value)
                added += 1
        return added

    def srem(self, key: str, *values: Any) -> int:
        """Mock SREM operation."""
        if key not in self._sets:
            return 0
        removed = 0
        for value in values:
            if value in self._sets[key]:
                self._sets[key].remove(value)
                removed += 1
        return removed

    def smembers(self, key: str) -> set:
        """Mock SMEMBERS operation."""
        return self._sets.get(key, set()).copy()

    def scard(self, key: str) -> int:
        """Mock SCARD operation."""
        return len(self._sets.get(key, set()))

    # Hash operations
    def hset(self, key: str, field: str, value: Any) -> int:
        """Mock HSET operation."""
        if key not in self._hashes:
            self._hashes[key] = {}
        is_new = field not in self._hashes[key]
        self._hashes[key][field] = value
        return 1 if is_new else 0

    def hget(self, key: str, field: str) -> Optional[Any]:
        """Mock HGET operation."""
        return self._hashes.get(key, {}).get(field)

    def hgetall(self, key: str) -> Dict[str, Any]:
        """Mock HGETALL operation."""
        return self._hashes.get(key, {}).copy()

    def hdel(self, key: str, *fields: str) -> int:
        """Mock HDEL operation."""
        if key not in self._hashes:
            return 0
        deleted = 0
        for field in fields:
            if field in self._hashes[key]:
                del self._hashes[key][field]
                deleted += 1
        return deleted

    def hexists(self, key: str, field: str) -> bool:
        """Mock HEXISTS operation."""
        return field in self._hashes.get(key, {})

    def hlen(self, key: str) -> int:
        """Mock HLEN operation."""
        return len(self._hashes.get(key, {}))

    # Pub/Sub operations (basic mock)
    def publish(self, channel: str, message: Any) -> int:
        """Mock PUBLISH operation."""
        return 1  # Assume one subscriber

    def pubsub(self):
        """Mock PUBSUB operation."""
        return MockPubSub()


class MockPubSub:
    """Mock implementation of Redis PubSub."""

    def __init__(self):
        self._subscriptions = set()
        self._patterns = set()

    def subscribe(self, *channels: str):
        """Mock SUBSCRIBE operation."""
        self._subscriptions.update(channels)

    def unsubscribe(self, *channels: str):
        """Mock UNSUBSCRIBE operation."""
        self._subscriptions.difference_update(channels)

    def psubscribe(self, *patterns: str):
        """Mock PSUBSCRIBE operation."""
        self._patterns.update(patterns)

    def punsubscribe(self, *patterns: str):
        """Mock PUNSUBSCRIBE operation."""
        self._patterns.difference_update(patterns)

    def get_message(self, timeout: Optional[float] = None):
        """Mock GET_MESSAGE operation."""
        return None  # No messages in mock

    def listen(self):
        """Mock LISTEN operation."""
        return iter([])  # Empty iterator


class MockRedisConnectionPool:
    """Mock Redis connection pool."""

    def __init__(self, **kwargs):
        self.connection_kwargs = kwargs

    def get_connection(self, command_name: str, **kwargs):
        """Mock get connection."""
        return MagicMock()

    def release(self, connection):
        """Mock release connection."""
        pass


# Factory function for creating mock Redis instances
def create_mock_redis(**kwargs) -> MockRedis:
    """Create a mock Redis instance with optional configuration."""
    return MockRedis(**kwargs)


# Mock Redis URL parsing
def mock_from_url(url: str, **kwargs) -> MockRedis:
    """Mock Redis.from_url method."""
    return MockRedis(**kwargs)
