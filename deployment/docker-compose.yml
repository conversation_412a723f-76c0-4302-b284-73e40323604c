services:
  backend:
    image: 008971661427.dkr.ecr.us-east-1.amazonaws.com/agentic-ai/incident-management:backend_776
    container_name: incident-management-api
    hostname: incident-management-api
    env_file: .env
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
      - ./static:/app/static
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: [ "CMD-SHELL", "curl -s http://0.0.0.0:8000/health | grep '\"status\":\"ok\"'" ]
      interval: 10s
      timeout: 5s
      retries: 3
    restart: unless-stopped

  celery:
    image: 008971661427.dkr.ecr.us-east-1.amazonaws.com/agentic-ai/incident-management:backend_776
    container_name: incident-management-celery
    hostname: incident-management-celery
    command: celery -A utils.celery_worker worker --loglevel=debug
    env_file: .env
    volumes:
      - ./logs:/app/logs
      - ./static:/app/static
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "celery -A utils.celery_worker inspect ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # celery-beat:
  #   image: 008971661427.dkr.ecr.us-east-1.amazonaws.com/agentic-ai/incident-management:backend_776
  #   container_name: incident-management-celery-beat
  #   hostname: incident-management-celery-beat
  #   command: celery -A utils.celery_worker beat --loglevel=info
  #   env_file: "backend/.env"
  #   volumes:
  #     - ./logs:/app/logs
  #     - ./static:/app/static
  #   depends_on:
  #     - celery
  #   restart: unless-stopped

  frontend:
    image: 008971661427.dkr.ecr.us-east-1.amazonaws.com/agentic-ai/incident-management:frontend_776
    container_name: incident-management-ui
    environment:
      - API_URL=http://**************:2000/api
    ports:
      - "2000:2000"
    depends_on:
      - backend
    healthcheck:
      test: [ "CMD-SHELL", "curl -s http://0.0.0.0:2000/health | grep '\"status\":\"ok\"'" ]
      interval: 10s
      timeout: 5s
      retries: 3
    restart: unless-stopped

  db:
    image: postgres:17
    container_name: incident-management-db
    env_file: .env
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: [ "CMD", "pg_isready", "-U", "incident_db_admin", "-d", "incident_db" ]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
  redis:
    image: redis:8.2.1-alpine
    container_name: incident-management-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: sh -c "test -f appendonly.aof || touch appendonly.aof ; echo y | redis-check-aof --fix appendonly.aof ; redis-server --save 60 1 --loglevel warning"
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 5s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # alloy:
  #   image: grafana/alloy:v1.10.0
  #   container_name: alloy
  #   volumes:
  #     - ./config/config.alloy:/etc/alloy/config.alloy
  #     - ../../var/log:/temp/logs/
  #   command: ["run", "/etc/alloy/config.alloy","--stability.level=experimental"]
  #   ports:
  #     - "12345:12345"
  #   depends_on:
  #     - loki
  #   restart: unless-stopped

  # grafana:
  #   image: grafana/grafana:12.1.0
  #   container_name: incident-management-grafana
  #   ports:
  #     - "3000:3000"
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #   depends_on:
  #     - loki
  #   environment:
  #     - GF_SECURITY_ADMIN_USER=admin
  #     - GF_SECURITY_ADMIN_PASSWORD=admin
  #     - GF_AUTH_ANONYMOUS_ENABLED=true
  #     - GF_AUTH_ANONYMOUS_ORG_ROLE=Viewer
  #     - GF_SECURITY_ALLOW_EMBEDDING=true
  #   restart: unless-stopped

  # loki:
  #   image: grafana/loki:3.5.3
  #   container_name: incident-management-loki
  #   command: "-config.file=/etc/loki/config.yaml"
  #   ports:
  #     - "3100:3100"
  #   volumes:
  #     - ./loki/config/config.yaml:/etc/loki/config.yaml:ro
  #     - loki_data:/loki:rw
  #   healthcheck:
  #     test: CMD-SHELL wget --quiet -O- http://0.0.0.0:3100/ready | grep "ready"
  #     interval: 1m30s
  #     timeout: 30s
  #     retries: 5
  #   restart: unless-stopped

  qdrant:
    image: qdrant/qdrant:v1.15.4
    container_name: qdrant
    environment:
      - QDRANT__TELEMETRY_DISABLED=true
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped

volumes:
  postgres_data:
  qdrant_data:
  redis_data:
  # loki_data:
  # grafana_data:
