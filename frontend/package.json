{"name": "incident-management-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "format": "prettier . --write", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "type-check": "tsc --noEmit", "prepare": "cd .. && husky frontend/.husky || true"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@mantine/charts": "^8.3.3", "@mantine/core": "^8.3.3", "@mantine/dates": "^8.3.3", "@mantine/hooks": "^8.3.3", "@tailwindcss/vite": "^4.1.14", "@tanstack/react-query": "^5.90.2", "dayjs": "^1.11.18", "lucide-react": "^0.545.0", "motion": "^12.23.22", "react": "^19.2.0", "react-dom": "^19.2.0", "react-hook-form": "^7.64.0", "react-markdown": "^10.1.0", "react-router": "^7.9.4", "react-toastify": "^11.0.5", "recharts": "^3.2.1", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "tailwindcss": "^4.1.14", "zod": "^4.1.12"}, "devDependencies": {"@eslint/js": "^9.37.0", "@tanstack/react-query-devtools": "^5.90.2", "@testing-library/jest-dom": "^6.9.1", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^24.7.0", "@types/react": "^19.2.2", "@types/react-dom": "^19.2.1", "@vitejs/plugin-react": "^5.0.4", "eslint": "^9.37.0", "eslint-plugin-react-hooks": "^7.0.0", "eslint-plugin-react-refresh": "^0.4.23", "globals": "^16.4.0", "husky": "^9.1.7", "jest": "^30.2.0", "jest-environment-jsdom": "^30.2.0", "prettier": "3.6.2", "ts-jest": "^29.4.4", "ts-node": "^10.9.2", "typescript": "~5.9.3", "typescript-eslint": "^8.46.0", "vite": "^7.1.9"}, "packageManager": "pnpm@9.15.2+sha512.93e57b0126f0df74ce6bff29680394c0ba54ec47246b9cf321f0121d8d9bb03f750a705f24edc3c1180853afd7c2c3b94196d0a3d53d3e069d9e2793ef11f321"}