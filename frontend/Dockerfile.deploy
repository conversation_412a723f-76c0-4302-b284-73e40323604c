# Deployment Dockerfile - assumes dist folder is already built locally
FROM nginx:alpine
# Default config for local development
ENV API_URL=http://localhost:2000/api

# Install curl for health checks and remove default nginx config
RUN apk add --no-cache curl && rm /etc/nginx/conf.d/default.conf

# Copy nginx configuration
COPY nginx/nginx.conf /etc/nginx/conf.d
COPY nginx/config-env.sh /docker-entrypoint.d/40-config-env.sh
RUN chmod +x /docker-entrypoint.d/40-config-env.sh

# Copy pre-built dist folder
COPY dist /usr/share/nginx/html

# Debug: verify nginx folder contents
RUN ls -la /usr/share/nginx/html

EXPOSE 2000
CMD ["nginx", "-g", "daemon off;"]
