import {
  ActionIcon,
  Card,
  Flex,
  Group,
  Menu,
  Stack,
  Text,
  Tooltip,
} from '@mantine/core';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import {
  Check,
  Clock,
  Edit,
  FolderOpen,
  MoreVertical,
  Plus,
  Trash2,
} from 'lucide-react';
import React from 'react';
import { ProjectListItem } from '../../types/KnowledgeBaseTypes';

interface ProjectCardProps {
  project: ProjectListItem;
  onEdit?: (project: ProjectListItem) => void;
  onDelete?: (project: ProjectListItem) => void;
  onViewKnowledgeBase?: (project: ProjectListItem) => void;
  isSelected?: boolean;
  onSelect?: (project: ProjectListItem) => void;
}

dayjs.extend(relativeTime);

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onEdit,
  onDelete,
  onViewKnowledgeBase,
  isSelected = false,
  onSelect,
}) => {
  const handleMenuAction = (action: string, event: React.MouseEvent) => {
    event.stopPropagation();

    switch (action) {
      case 'select':
        onSelect?.(project);
        break;
      case 'edit':
        onEdit?.(project);
        break;
      case 'delete':
        onDelete?.(project);
        break;
      case 'view-kb':
        onViewKnowledgeBase?.(project);
        break;
    }
  };

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder h={180}>
      <Stack h="100%" justify="space-between" gap="xs">
        <Group justify="space-between" align="center" wrap="nowrap">
          <Group gap="xs" flex={1} miw={0}>
            <FolderOpen size={20} className="text-blue-600" />
            <Tooltip label={project.name} disabled={project.name.length <= 30}>
              <Text fw={600} size="sm" truncate flex={1} ta={'left'}>
                {project.name}
              </Text>
            </Tooltip>
          </Group>

          <Menu shadow="md" width={200} position="bottom-end">
            <Menu.Target>
              <ActionIcon
                variant="subtle"
                size="sm"
                onClick={e => e.stopPropagation()}
              >
                <MoreVertical size={16} />
              </ActionIcon>
            </Menu.Target>

            <Menu.Dropdown>
              {!isSelected && (
                <Menu.Item
                  leftSection={<Check size={14} />}
                  onClick={e => handleMenuAction('select', e)}
                >
                  Select Project
                </Menu.Item>
              )}
              <Menu.Item
                leftSection={<Edit size={14} />}
                onClick={e => handleMenuAction('edit', e)}
              >
                Edit Project
              </Menu.Item>
              <Menu.Item
                leftSection={<Plus size={14} />}
                onClick={e => handleMenuAction('view-kb', e)}
              >
                View Knowledge Base
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item
                leftSection={<Trash2 size={14} />}
                color="red"
                onClick={e => handleMenuAction('delete', e)}
              >
                Delete Project
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>

        <Group gap="xs" wrap="wrap">
          {isSelected && (
            <Text size="xs" c="green" fw={500}>
              Active Project
            </Text>
          )}
        </Group>

        {/* Lines 3-4: Description */}
        <Flex flex={1} direction="column">
          {project.description ? (
            <Tooltip
              label={project.description}
              disabled={project.description.length <= 120}
              multiline
              w={300}
            >
              <Text size="xs" c="dimmed" lineClamp={2}>
                {project.description}
              </Text>
            </Tooltip>
          ) : (
            <Text size="xs" c="dimmed" fs="italic">
              No description available
            </Text>
          )}
        </Flex>

        <Group justify="space-between" align="center">
          <Text size="xs" c="dimmed">
            {project.knowledge_base_count} knowledge base
            {project.knowledge_base_count !== 1 ? 's' : ''}
          </Text>

          <Tooltip
            label={`Updated: ${dayjs(project.updated_at).format('YYYY-MM-DD HH:mm:ss')}`}
          >
            <Group gap={4} wrap="nowrap">
              <Clock size={12} className="text-gray-500" />
              <Text size="xs" c="dimmed" truncate>
                Updated {dayjs(project.updated_at).fromNow()}
              </Text>
            </Group>
          </Tooltip>
        </Group>
      </Stack>
    </Card>
  );
};

export default ProjectCard;
