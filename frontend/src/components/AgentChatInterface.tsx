import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import {
  Paper,
  Text,
  Button,
  Textarea,
  Group,
  ScrollArea,
  Box,
  Stack,
  ActionIcon,
  Tooltip,
  Alert,
  Modal,
} from '@mantine/core';
import {
  Mic,
  Send,
  Square,
  MessageSquare,
  Bot,
  User,
  AlertCircle,
  File,
  Paperclip,
  X,
  Database,
  Check,
  Settings,
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';

import { ChatMessage } from '../types/Agent';
import {
  useChat,
  handleFileSelect,
  removeFile,
  getFileInputAccept,
  formatFileSize,
  messageVariants,
  getEventIcon,
} from '../utils';

interface AgentChatInterfaceProps {
  appName: string;
  agentName: string;
  userId: string;
  sessionId?: string;
  enableLiveMode?: boolean;
  className?: string;
  initialSessionState?: any;
}

export const AgentChatInterface: React.FC<AgentChatInterfaceProps> = ({
  appName,
  agentName,
  userId,
  sessionId: initialSessionId,
  enableLiveMode = false,
  className,
  initialSessionState,
}) => {
  // Additional UI state not handled by useChat
  const [commandModalOpened, setCommandModalOpened] = useState(false);

  // Refs for UI interactions
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Use the centralized chat functionality
  const {
    messages,
    currentInput,
    setCurrentInput,
    selectedFiles,
    setSelectedFiles,
    chatState,
    sendMessage,
    stopProcessing,
    clearChat,
    handleVoiceInput,
  } = useChat({
    appName,
    agentName,
    userId,
    sessionId: initialSessionId,
    enableLiveMode,
    initialSessionState,
  });

  // Auto-scroll to bottom
  const scrollToBottom = useCallback(() => {
    if (scrollAreaRef.current) {
      const viewport = scrollAreaRef.current.querySelector(
        '[data-scrollarea-viewport]',
      );
      if (viewport) {
        viewport.scrollTo({ top: viewport.scrollHeight, behavior: 'smooth' });
      }
    }
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // Focus input when not processing
  useEffect(() => {
    if (!chatState.isProcessing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [chatState.isProcessing]);

  // File handling
  const handleFileInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const result = handleFileSelect(event, error => {
        console.error('File upload error:', error);
      });

      if (result.newFiles.length > 0) {
        setSelectedFiles(prev => [...prev, ...result.newFiles]);
      }
    },
    [setSelectedFiles],
  );

  const handleRemoveFile = useCallback(
    (index: number) => {
      setSelectedFiles(prev => removeFile(index, prev));
    },
    [setSelectedFiles],
  );

  // Message sending
  const handleSendMessage = useCallback(async () => {
    if (
      (!currentInput.trim() && selectedFiles.length === 0) ||
      chatState.isProcessing
    ) {
      return;
    }
    await sendMessage();
  }, [currentInput, selectedFiles, chatState.isProcessing, sendMessage]);

  const handleKeyPress = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSendMessage();
      }
    },
    [handleSendMessage],
  );

  // Command actions
  const handleNewChat = useCallback(() => {
    clearChat();
    setCommandModalOpened(false);
  }, [clearChat]);

  const renderMessage = (message: ChatMessage) => {
    const isUser = message.type === 'user';
    const isEvent = message.type === 'event';

    if (isEvent) {
      return (
        <motion.div
          key={message.id}
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={messageVariants}
          style={{ marginBottom: '16px' }}
        >
          <Group gap="xs" style={{ justifyContent: 'center' }}>
            {getEventIcon(message.eventType, { size: 14 })}
            <Text
              size="xs"
              c="dimmed"
              style={{
                fontStyle:
                  message.eventType === 'thought' ? 'italic' : 'normal',
                fontWeight: message.eventType === 'thought' ? 500 : 'normal',
              }}
            >
              {message.eventType === 'thought' && '💭 '}
              {message.content}
            </Text>
          </Group>
        </motion.div>
      );
    }

    return (
      <motion.div
        key={message.id}
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={messageVariants}
        style={{
          marginBottom: '16px',
          display: 'flex',
          justifyContent: isUser ? 'flex-end' : 'flex-start',
        }}
      >
        <Group
          align="flex-start"
          gap="sm"
          style={{
            flexDirection: isUser ? 'row-reverse' : 'row',
            maxWidth: '100%',
            width: '100%',
          }}
        >
          <ActionIcon
            variant="light"
            color={isUser ? 'blue' : 'gray'}
            size="lg"
            radius="xl"
            style={{ flexShrink: 0 }}
          >
            {isUser ? <User size={20} /> : <Bot size={20} />}
          </ActionIcon>
          <Box style={{ flex: 1, minWidth: 0, maxWidth: '80%' }}>
            <Paper
              p="sm"
              radius="md"
              style={{
                backgroundColor: isUser
                  ? 'var(--mantine-color-blue-light)'
                  : 'var(--mantine-color-gray-0)',
              }}
            >
              {/* Main message content */}
              <Box
                style={{
                  fontSize: '14px',
                  lineHeight: '1.5',
                }}
              >
                <ReactMarkdown
                  remarkPlugins={[remarkGfm, remarkBreaks]}
                  rehypePlugins={[rehypeHighlight, rehypeRaw, rehypeSanitize]}
                  components={{
                    p: ({ children }) => (
                      <Text
                        size="sm"
                        style={{
                          margin: '0 0 8px 0',
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-word',
                          overflowWrap: 'break-word',
                        }}
                      >
                        {children}
                      </Text>
                    ),
                    code: ({ children, className }) => {
                      const isInline = !className?.includes('language-');

                      if (isInline) {
                        return (
                          <code
                            style={{
                              fontFamily:
                                'var(--mantine-font-family-monospace)',
                              fontSize: '14px',
                              backgroundColor: 'var(--mantine-color-gray-2)',
                              color: 'var(--mantine-color-dark-6)',
                              padding: '2px 4px',
                              borderRadius: '4px',
                              whiteSpace: 'nowrap',
                            }}
                          >
                            {children}
                          </code>
                        );
                      }

                      // For code blocks, let hljs handle the styling
                      return (
                        <pre className={className}>
                          <code className={className}>{children}</code>
                        </pre>
                      );
                    },
                    blockquote: ({ children }) => (
                      <Box
                        style={{
                          borderLeft: '4px solid var(--mantine-color-gray-4)',
                          paddingLeft: '16px',
                          margin: '8px 0',
                          fontStyle: 'italic',
                        }}
                      >
                        {children}
                      </Box>
                    ),
                    ul: ({ children }) => (
                      <Box
                        component="ul"
                        style={{ margin: '8px 0', paddingLeft: '20px' }}
                      >
                        {children}
                      </Box>
                    ),
                    ol: ({ children }) => (
                      <Box
                        component="ol"
                        style={{ margin: '8px 0', paddingLeft: '20px' }}
                      >
                        {children}
                      </Box>
                    ),
                    li: ({ children }) => (
                      <Text
                        component="li"
                        size="sm"
                        style={{ margin: '4px 0' }}
                      >
                        {children}
                      </Text>
                    ),
                    h1: ({ children }) => (
                      <Text
                        size="xl"
                        fw={700}
                        style={{ margin: '16px 0 8px 0' }}
                      >
                        {children}
                      </Text>
                    ),
                    h2: ({ children }) => (
                      <Text
                        size="lg"
                        fw={600}
                        style={{ margin: '16px 0 8px 0' }}
                      >
                        {children}
                      </Text>
                    ),
                    h3: ({ children }) => (
                      <Text
                        size="md"
                        fw={600}
                        style={{ margin: '12px 0 6px 0' }}
                      >
                        {children}
                      </Text>
                    ),
                    h4: ({ children }) => (
                      <Text
                        size="sm"
                        fw={600}
                        style={{ margin: '12px 0 6px 0' }}
                      >
                        {children}
                      </Text>
                    ),
                    h5: ({ children }) => (
                      <Text
                        size="sm"
                        fw={500}
                        style={{ margin: '12px 0 6px 0' }}
                      >
                        {children}
                      </Text>
                    ),
                    h6: ({ children }) => (
                      <Text
                        size="xs"
                        fw={500}
                        style={{ margin: '12px 0 6px 0' }}
                      >
                        {children}
                      </Text>
                    ),
                    strong: ({ children }) => (
                      <Text component="strong" fw={700}>
                        {children}
                      </Text>
                    ),
                    em: ({ children }) => (
                      <Text component="em" style={{ fontStyle: 'italic' }}>
                        {children}
                      </Text>
                    ),
                    a: ({ children, href }) => (
                      <Text
                        component="a"
                        href={href}
                        style={{
                          color: 'var(--mantine-color-blue-6)',
                          textDecoration: 'underline',
                        }}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {children}
                      </Text>
                    ),
                    table: ({ children }) => (
                      <Box
                        style={{
                          overflowX: 'auto',
                          margin: '8px 0',
                        }}
                      >
                        <table
                          style={{
                            width: '100%',
                            borderCollapse: 'collapse',
                            border: '1px solid var(--mantine-color-gray-3)',
                          }}
                        >
                          {children}
                        </table>
                      </Box>
                    ),
                    th: ({ children }) => (
                      <Text
                        component="th"
                        size="sm"
                        fw={600}
                        style={{
                          padding: '8px 12px',
                          border: '1px solid var(--mantine-color-gray-3)',
                          backgroundColor: 'var(--mantine-color-gray-1)',
                          textAlign: 'left',
                        }}
                      >
                        {children}
                      </Text>
                    ),
                    td: ({ children }) => (
                      <Text
                        component="td"
                        size="sm"
                        style={{
                          padding: '8px 12px',
                          border: '1px solid var(--mantine-color-gray-3)',
                        }}
                      >
                        {children}
                      </Text>
                    ),
                  }}
                >
                  {message.content}
                </ReactMarkdown>
              </Box>

              {/* Render inline data (images, files, artifacts) */}
              {message.metadata?.inlineData && (
                <Box mt="sm">
                  {message.metadata.inlineData.mimeType?.startsWith(
                    'image/',
                  ) ? (
                    <img
                      src={message.metadata.inlineData.data}
                      alt={message.metadata.inlineData.displayName || 'Image'}
                      style={{
                        maxWidth: '100%',
                        height: 'auto',
                        borderRadius: '8px',
                        border: '1px solid var(--mantine-color-gray-3)',
                      }}
                    />
                  ) : (
                    <Paper p="xs" withBorder radius="sm">
                      <Group gap="xs">
                        <File size={16} />
                        <Text size="sm">
                          {message.metadata.inlineData.displayName || 'File'}
                        </Text>
                      </Group>
                    </Paper>
                  )}
                </Box>
              )}

              {/* Render executable code */}
              {message.metadata?.executableCode && (
                <Box mt="sm">
                  <Paper
                    p="sm"
                    withBorder
                    radius="sm"
                    bg="var(--mantine-color-gray-0)"
                  >
                    <Text size="xs" c="dimmed" mb="xs">
                      {message.metadata.executableCode.language || 'Code'}
                    </Text>
                    <Text
                      size="sm"
                      ff="monospace"
                      style={{
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-all',
                        overflowWrap: 'break-word',
                        backgroundColor: 'var(--mantine-color-dark-8)',
                        color: 'var(--mantine-color-gray-0)',
                        padding: '8px',
                        borderRadius: '4px',
                      }}
                    >
                      {message.metadata.executableCode.code}
                    </Text>
                  </Paper>
                </Box>
              )}

              {/* Render code execution results */}
              {message.metadata?.codeExecutionResult && (
                <Box mt="sm">
                  <Paper
                    p="sm"
                    withBorder
                    radius="sm"
                    bg="var(--mantine-color-green-0)"
                  >
                    <Text size="xs" c="dimmed" mb="xs">
                      Execution Result:{' '}
                      {message.metadata.codeExecutionResult.outcome}
                    </Text>
                    {message.metadata.codeExecutionResult.output && (
                      <Text
                        size="sm"
                        ff="monospace"
                        style={{
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-all',
                          overflowWrap: 'break-word',
                          backgroundColor: 'var(--mantine-color-dark-8)',
                          color: 'var(--mantine-color-gray-0)',
                          padding: '8px',
                          borderRadius: '4px',
                        }}
                      >
                        {message.metadata.codeExecutionResult.output}
                      </Text>
                    )}
                  </Paper>
                </Box>
              )}

              {/* Render function call details */}
              {message.metadata?.functionCall && (
                <Box mt="sm">
                  <Paper
                    p="sm"
                    withBorder
                    radius="sm"
                    bg="var(--mantine-color-blue-0)"
                  >
                    <Group gap="xs" mb="xs">
                      <Database size={16} color="var(--mantine-color-blue-6)" />
                      <Text size="sm" fw={500} c="var(--mantine-color-blue-6)">
                        Tool Call: {message.metadata.functionCall.name}
                      </Text>
                    </Group>
                    {message.metadata.functionCall.args && (
                      <Text
                        size="xs"
                        ff="monospace"
                        c="dimmed"
                        style={{
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-all',
                          overflowWrap: 'break-word',
                        }}
                      >
                        {JSON.stringify(
                          message.metadata.functionCall.args,
                          null,
                          2,
                        )}
                      </Text>
                    )}
                  </Paper>
                </Box>
              )}

              {/* Render function response details */}
              {message.metadata?.functionResponse && (
                <Box mt="sm">
                  <Paper
                    p="sm"
                    withBorder
                    radius="sm"
                    bg="var(--mantine-color-green-0)"
                  >
                    <Group gap="xs" mb="xs">
                      <Check size={16} color="var(--mantine-color-green-6)" />
                      <Text size="sm" fw={500} c="var(--mantine-color-green-6)">
                        Tool Response: {message.metadata.functionResponse.name}
                      </Text>
                    </Group>
                    {message.metadata.functionResponse.response && (
                      <Text
                        size="xs"
                        ff="monospace"
                        c="dimmed"
                        style={{
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-all',
                          overflowWrap: 'break-word',
                        }}
                      >
                        {typeof message.metadata.functionResponse.response ===
                        'string'
                          ? message.metadata.functionResponse.response
                          : JSON.stringify(
                              message.metadata.functionResponse.response,
                              null,
                              2,
                            )}
                      </Text>
                    )}
                  </Paper>
                </Box>
              )}

              {/* Render grounding metadata/search results */}
              {message.metadata?.renderedContent && (
                <Box mt="sm">
                  <Text size="xs" c="dimmed" mb="xs">
                    Search Results:
                  </Text>
                  <div
                    dangerouslySetInnerHTML={{
                      __html: message.metadata.renderedContent,
                    }}
                  />
                </Box>
              )}

              {/* Render user attachments */}
              {message.metadata?.attachments && (
                <Stack gap="xs" mt="xs">
                  {message.metadata.attachments.map(
                    (attachment: any, index: number) => (
                      <Paper key={index} p="xs" withBorder radius="sm">
                        <Group gap="xs">
                          <File size={14} />
                          <Text size="xs">{attachment.file.name}</Text>
                          <Text size="xs" c="dimmed">
                            ({formatFileSize(attachment.file.size)})
                          </Text>
                        </Group>
                      </Paper>
                    ),
                  )}
                </Stack>
              )}
            </Paper>
          </Box>
        </Group>
      </motion.div>
    );
  };

  return (
    <>
      <Paper
        h="100%"
        style={{
          display: 'flex',
          flexDirection: 'column',
          border: '1px solid var(--mantine-color-gray-3)',
        }}
        className={className}
      >
        {/* Header */}
        <Paper
          p="md"
          style={{
            borderBottom: '1px solid var(--mantine-color-gray-3)',
            backgroundColor: 'var(--mantine-color-gray-0)',
          }}
        >
          <Group justify="space-between" align="center">
            <Group gap="sm">
              <MessageSquare size={20} />
              <div>
                <Text fw={600}>Abilytics Agent</Text>
                <Text size="xs" c="dimmed">
                  {agentName} -{' '}
                  {chatState.isConnected ? 'Connected' : 'Disconnected'}
                </Text>
              </div>
            </Group>
            <Group gap="xs">
              {chatState.isProcessing && (
                <Button
                  variant="light"
                  color="red"
                  size="xs"
                  leftSection={<Square size={14} />}
                  onClick={stopProcessing}
                >
                  Stop
                </Button>
              )}
              <ActionIcon
                variant="light"
                onClick={() => setCommandModalOpened(true)}
              >
                <Settings size={16} />
              </ActionIcon>
            </Group>
          </Group>
        </Paper>

        {/* Messages */}
        <ScrollArea
          flex={1}
          ref={scrollAreaRef}
          style={{
            padding: '16px',
            overflowX: 'hidden',
          }}
        >
          <AnimatePresence>{messages.map(renderMessage)}</AnimatePresence>
          <div ref={messagesEndRef} />
        </ScrollArea>

        {/* Error Display */}
        {chatState.error && (
          <Alert
            icon={<AlertCircle size={16} />}
            color="red"
            variant="light"
            m="md"
            mb={0}
          >
            {chatState.error}
          </Alert>
        )}

        {/* File Attachments */}
        {selectedFiles.length > 0 && (
          <Paper
            p="sm"
            m="md"
            mb={0}
            style={{ backgroundColor: 'var(--mantine-color-gray-0)' }}
          >
            <Stack gap="xs">
              <Text size="sm" fw={500}>
                Attachments ({selectedFiles.length})
              </Text>
              {selectedFiles.map((file, index) => (
                <Group key={index} justify="space-between">
                  <Group gap="xs">
                    <File size={14} />
                    <Text size="xs">
                      {file.file.name} ({formatFileSize(file.file.size)})
                    </Text>
                  </Group>
                  <ActionIcon
                    size="sm"
                    variant="subtle"
                    color="red"
                    onClick={() => handleRemoveFile(index)}
                  >
                    <X size={12} />
                  </ActionIcon>
                </Group>
              ))}
            </Stack>
          </Paper>
        )}

        {/* Input */}
        <Paper
          p="md"
          style={{ borderTop: '1px solid var(--mantine-color-gray-3)' }}
        >
          <Group gap="sm" align="flex-end">
            <Box style={{ flex: 1 }}>
              <Textarea
                ref={inputRef}
                placeholder="Type your message..."
                value={currentInput}
                onChange={e => setCurrentInput(e.target.value)}
                onKeyDown={handleKeyPress}
                autosize
                minRows={1}
                maxRows={4}
                disabled={chatState.isProcessing}
              />
            </Box>
            <Group gap="xs">
              <Tooltip label="Attach files">
                <ActionIcon
                  variant="light"
                  onClick={() => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = getFileInputAccept();
                    input.multiple = true;
                    input.onchange = e => {
                      const event = e as any;
                      handleFileInputChange(event);
                    };
                    input.click();
                  }}
                  disabled={chatState.isProcessing}
                >
                  <Paperclip size={16} />
                </ActionIcon>
              </Tooltip>
              <Tooltip label="Voice input">
                <ActionIcon
                  variant="light"
                  onClick={handleVoiceInput}
                  disabled={chatState.isProcessing}
                >
                  <Mic size={16} />
                </ActionIcon>
              </Tooltip>
              <Button
                leftSection={<Send size={16} />}
                onClick={handleSendMessage}
                disabled={
                  (!currentInput.trim() && selectedFiles.length === 0) ||
                  chatState.isProcessing
                }
                loading={chatState.isProcessing}
              >
                Send
              </Button>
            </Group>
          </Group>
        </Paper>
      </Paper>

      {/* Command Modal */}
      <Modal
        opened={commandModalOpened}
        onClose={() => setCommandModalOpened(false)}
        title="Chat Commands"
        size="sm"
      >
        <Stack gap="sm">
          <Button
            variant="light"
            leftSection={<MessageSquare size={16} />}
            onClick={handleNewChat}
          >
            New Chat
          </Button>
        </Stack>
      </Modal>
    </>
  );
};

export default AgentChatInterface;
