import {
  ActionIcon,
  Badge,
  Card,
  Flex,
  Group,
  Menu,
  Stack,
  Text,
  Tooltip,
} from '@mantine/core';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import {
  BookO<PERSON>,
  Clock,
  Edit,
  MoreVertical,
  Trash2,
  Upload,
} from 'lucide-react';
import React from 'react';
import { useNavigate } from 'react-router';
import { KnowledgeBase } from '../../types/KnowledgeBaseTypes';

dayjs.extend(relativeTime);

interface KnowledgeBaseCardProps {
  knowledgeBase: KnowledgeBase;
  onEdit?: (kb: KnowledgeBase) => void;
  onDelete?: (kb: KnowledgeBase) => void;
  onUploadDocument?: (kb: KnowledgeBase) => void;
}

const KnowledgeBaseCard: React.FC<KnowledgeBaseCardProps> = ({
  knowledgeBase,
  onEdit,
  onDelete,
  onUploadDocument,
}) => {
  const navigate = useNavigate();

  const handleCardClick = () => {
    navigate(`/knowledge-base/${knowledgeBase.id}`);
  };

  const handleMenuAction = (action: string, event: React.MouseEvent) => {
    event.stopPropagation();

    switch (action) {
      case 'edit':
        onEdit?.(knowledgeBase);
        break;
      case 'delete':
        onDelete?.(knowledgeBase);
        break;
      case 'upload':
        onUploadDocument?.(knowledgeBase);
        break;
    }
  };

  const getKbTypeColor = (type: string) => {
    switch (type) {
      case 'PROJECT_DOCUMENTATION':
        return 'blue';
      case 'EXTERNAL_DOCUMENTATION':
        return 'green';
      case 'SYSTEM_ARCHITECTURE':
        return 'purple';
      case 'SERVICE_DETAILS':
        return 'orange';
      case 'RUNBOOKS':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getKbTypeLabel = (type: string) => {
    return type
      .replace(/_/g, ' ')
      .toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <Card
      shadow="sm"
      padding="lg"
      radius="md"
      withBorder
      className="cursor-pointer hover:shadow-md transition-shadow"
      onClick={handleCardClick}
      h={180}
    >
      <Stack h="100%" justify="space-between" gap="xs">
        <Group justify="space-between" align="center" wrap="nowrap">
          <Group gap="xs" flex={1} miw={0}>
            <BookOpen size={20} className="text-green-600" />
            <Tooltip
              label={knowledgeBase.name}
              disabled={knowledgeBase.name.length <= 30}
            >
              <Text fw={600} size="sm" truncate flex={1} ta={'left'}>
                {knowledgeBase.name}
              </Text>
            </Tooltip>
          </Group>

          <Menu shadow="md" width={200} position="bottom-end">
            <Menu.Target>
              <ActionIcon
                variant="subtle"
                size="sm"
                onClick={e => e.stopPropagation()}
              >
                <MoreVertical size={16} />
              </ActionIcon>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Item
                leftSection={<Edit size={14} />}
                onClick={e => handleMenuAction('edit', e)}
              >
                Edit Knowledge Base
              </Menu.Item>
              <Menu.Item
                leftSection={<Upload size={14} />}
                onClick={e => handleMenuAction('upload', e)}
              >
                Upload Document
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item
                leftSection={<Trash2 size={14} />}
                color="red"
                onClick={e => handleMenuAction('delete', e)}
              >
                Delete Knowledge Base
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>

        <Group gap="xs" wrap="wrap">
          <Badge
            color={getKbTypeColor(knowledgeBase.kb_type)}
            variant="light"
            size="xs"
          >
            {getKbTypeLabel(knowledgeBase.kb_type)}
          </Badge>
        </Group>
        <Flex flex={1} direction="column">
          {knowledgeBase.description ? (
            <Tooltip
              label={knowledgeBase.description}
              disabled={knowledgeBase.description.length <= 120}
              multiline
              w={300}
            >
              <Text size="xs" c="dimmed" lineClamp={2}>
                {knowledgeBase.description}
              </Text>
            </Tooltip>
          ) : (
            <Text size="xs" c="dimmed" fs="italic">
              No description available
            </Text>
          )}
        </Flex>
        <Group justify="space-between" align="center">
          <Text size="xs" c="dimmed">
            {knowledgeBase.documents.length} document
            {knowledgeBase.documents.length !== 1 ? 's' : ''}
          </Text>

          <Tooltip
            label={`Updated: ${dayjs(knowledgeBase.updated_at).format('YYYY-MM-DD HH:mm:ss')}`}
          >
            <Group gap={4} wrap="nowrap">
              <Clock size={12} className="text-gray-500" />
              <Text size="xs" c="dimmed" truncate>
                Updated {dayjs(knowledgeBase.updated_at).fromNow()}
              </Text>
            </Group>
          </Tooltip>
        </Group>
      </Stack>
    </Card>
  );
};

export default KnowledgeBaseCard;
