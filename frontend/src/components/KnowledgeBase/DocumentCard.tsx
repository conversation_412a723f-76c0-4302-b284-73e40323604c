import {
  ActionIcon,
  Badge,
  Card,
  Flex,
  Group,
  Menu,
  Stack,
  Text,
  Tooltip,
} from '@mantine/core';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import {
  Clock,
  Download,
  Edit,
  Eye,
  File,
  FileImage,
  FileText,
  Link,
  MoreVertical,
  Trash2,
} from 'lucide-react';
import React from 'react';
import {
  DocumentListItem,
  SyncStatusEnum,
} from '../../types/KnowledgeBaseTypes';

dayjs.extend(relativeTime);

interface DocumentCardProps {
  document: DocumentListItem;
  onView?: (doc: DocumentListItem) => void;
  onEdit?: (doc: DocumentListItem) => void;
  onDelete?: (doc: DocumentListItem) => void;
  onDownload?: (doc: DocumentListItem) => void;
}

const DocumentCard: React.FC<DocumentCardProps> = ({
  document,
  onView,
  onEdit,
  onDelete,
  onDownload,
}) => {
  const handleMenuAction = (action: string, event: React.MouseEvent) => {
    event.stopPropagation();

    switch (action) {
      case 'view':
        onView?.(document);
        break;
      case 'edit':
        onEdit?.(document);
        break;
      case 'delete':
        onDelete?.(document);
        break;
      case 'download':
        onDownload?.(document);
        break;
    }
  };

  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'PDF':
        return <File size={20} className="text-red-600" />;
      case 'TEXT':
      case 'MARKDOWN':
        return <FileText size={20} className="text-blue-600" />;
      case 'URL':
        return <Link size={20} className="text-green-600" />;
      case 'DOCX':
        return <FileImage size={20} className="text-blue-800" />;
      default:
        return <File size={20} className="text-gray-600" />;
    }
  };

  const getSyncStatusColor = (status?: string) => {
    switch (status) {
      case SyncStatusEnum.SYNCED:
        return 'green';
      case SyncStatusEnum.PROCESSING:
        return 'yellow';
      case SyncStatusEnum.PENDING:
        return 'blue';
      case SyncStatusEnum.FAILED:
        return 'red';
      default:
        return 'gray';
    }
  };

  const getSyncStatusLabel = (status?: string) => {
    if (!status) return 'Unknown';
    return status.charAt(0) + status.slice(1).toLowerCase();
  };

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder h={180}>
      <Stack h="100%" justify="space-between" gap="xs">
        <Group justify="space-between" align="center" wrap="nowrap">
          <Group gap="xs" flex={1} miw={0}>
            {getDocumentIcon(document.document_type)}
            <Tooltip
              label={document.name}
              disabled={document.name.length <= 30}
            >
              <Text fw={600} size="sm" truncate flex={1} ta={'left'}>
                {document.name}
              </Text>
            </Tooltip>
          </Group>

          <Menu shadow="md" width={200} position="bottom-end">
            <Menu.Target>
              <ActionIcon
                variant="subtle"
                size="sm"
                onClick={e => e.stopPropagation()}
              >
                <MoreVertical size={16} />
              </ActionIcon>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Item
                leftSection={<Eye size={14} />}
                onClick={e => handleMenuAction('view', e)}
              >
                View Document
              </Menu.Item>
              <Menu.Item
                leftSection={<Edit size={14} />}
                onClick={e => handleMenuAction('edit', e)}
              >
                Edit Document
              </Menu.Item>
              <Menu.Item
                leftSection={<Download size={14} />}
                onClick={e => handleMenuAction('download', e)}
              >
                Download
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item
                leftSection={<Trash2 size={14} />}
                color="red"
                onClick={e => handleMenuAction('delete', e)}
              >
                Delete Document
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
        <Group gap="xs" wrap="wrap">
          <Badge variant="light" size="xs">
            {document.document_type}
          </Badge>
          {document.sync_status && (
            <Badge
              color={getSyncStatusColor(document.sync_status)}
              variant="light"
              size="xs"
            >
              {getSyncStatusLabel(document.sync_status)}
            </Badge>
          )}
        </Group>

        <Flex flex={1} direction="column">
          {document.description ? (
            <Tooltip
              label={document.description}
              disabled={document.description.length <= 120}
              multiline
              w={300}
            >
              <Text size="xs" c="dimmed" lineClamp={2}>
                {document.description}
              </Text>
            </Tooltip>
          ) : (
            <Text size="xs" c="dimmed" fs="italic">
              No description available
            </Text>
          )}
        </Flex>

        <Group justify="flex-end" align="center">
          <Tooltip
            label={`Updated: ${dayjs(document.updated_at).format('YYYY-MM-DD HH:mm:ss')}`}
          >
            <Group gap={4} wrap="nowrap">
              <Clock size={12} className="text-gray-500" />
              <Text size="xs" c="dimmed" truncate>
                Updated {dayjs(document.updated_at).fromNow()}
              </Text>
            </Group>
          </Tooltip>
        </Group>
      </Stack>
    </Card>
  );
};

export default DocumentCard;
