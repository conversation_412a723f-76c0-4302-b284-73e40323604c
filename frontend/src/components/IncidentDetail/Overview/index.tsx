import { Grid, Stack, Loader, Center } from '@mantine/core';
import { useParams } from 'react-router';
import { Suspense, lazy } from 'react';
import { queryIncidentById, queryIncidentDetails } from '../../../hooks/useApi';

// Lazy load overview sub-components
const AffectedServices = lazy(() => import('./AffectedServices'));
const AssignedUsers = lazy(() => import('./AssignedUsers'));
const Summary = lazy(() => import('./Summary'));
const QuickActions = lazy(() => import('./QuickActions'));
const StatusHistory = lazy(() => import('./StatusHistory'));
const TimelinePreview = lazy(() => import('./TimelinePreview'));
const SimilarIncidents = lazy(() => import('./SimilarIncidents'));
const Details = lazy(() => import('./Details'));

const IncidentOverview = () => {
  const { incidentId } = useParams<{ incidentId: string }>();
  if (!incidentId) {
    return null;
  }
  const { data: incident } = queryIncidentById(incidentId);
  const { data: incidentDetails } = queryIncidentDetails(incidentId);

  if (!incident) return null;

  // Loading fallback component
  const LoadingFallback = () => (
    <Center p="sm">
      <Loader size="xs" />
    </Center>
  );

  return (
    <Grid>
      <Grid.Col span={12}>
        <Stack gap="lg">
          <Grid>
            {/* Left Column */}
            <Grid.Col span={{ base: 12, md: 8 }}>
              <Stack gap="md">
                <Suspense fallback={<LoadingFallback />}>
                  <Summary summary={incident.summary || 'No summary available'} />
                </Suspense>
                <Suspense fallback={<LoadingFallback />}>
                  <Details
                    details={
                      incidentDetails?.incident_details || 'No details available'
                    }
                  />
                </Suspense>
                <Suspense fallback={<LoadingFallback />}>
                  <TimelinePreview />
                </Suspense>
                <Suspense fallback={<LoadingFallback />}>
                  <SimilarIncidents incidentId={incidentId} />
                </Suspense>
              </Stack>
            </Grid.Col>

            {/* Right Column*/}
            <Grid.Col span={{ base: 12, md: 4 }}>
              <Stack gap="md">
                <Suspense fallback={<LoadingFallback />}>
                  <QuickActions />
                </Suspense>
                <Suspense fallback={<LoadingFallback />}>
                  <StatusHistory />
                </Suspense>
                <Suspense fallback={<LoadingFallback />}>
                  <AffectedServices
                    services={
                      incidentDetails?.affected_services?.map(serviceName => ({
                        id: serviceName,
                        name: serviceName,
                        status: 'Operational' as const,
                        details: [],
                        impact: 'Medium' as const,
                        dependencies: [],
                      })) || []
                    }
                  />
                </Suspense>
                <Suspense fallback={<LoadingFallback />}>
                  <AssignedUsers users={incident.assignedUsers} />
                </Suspense>
              </Stack>
            </Grid.Col>
          </Grid>
        </Stack>
      </Grid.Col>
    </Grid>
  );
};

export default IncidentOverview;
