import { Card, Title, Group, ActionIcon, Textarea } from '@mantine/core';
import { Pen, X } from 'lucide-react';
import { useState } from 'react';

const RootCauseAnalysis = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempText, setTempText] = useState(
    `On May 13, 2025, at 05:55 UTC, the Payment API experienced a critical failure due to parameter validation issues introduced in deployment v3.2.0. The incident affected 1,250 users and resulted in 35 minutes of downtime with an estimated financial impact of $15,000.`,
  );
  const handleEdit = () => {
    setIsEditing(!isEditing);
  };

  return (
    <Card padding="lg" withBorder>
      <Group justify="space-between" align="center" mb="md">
        <div /> {/* Empty div to balance the layout */}
        <Title order={3}>Root Cause Analysis</Title>
        <ActionIcon size="lg" variant="light" color="blue" onClick={handleEdit}>
          {isEditing ? <X size={16} /> : <Pen size={16} />}
        </ActionIcon>
      </Group>
      {isEditing ? (
        <Textarea
          value={tempText}
          onChange={event => setTempText(event.currentTarget.value)}
          autoFocus
          variant="filled"
          autosize
          mb="md"
          styles={{
            input: {
              padding: '8px',
              border: '1px dashed #ccc',
              borderRadius: '4px',
            },
          }}
        />
      ) : (
        <Textarea
          value={tempText}
          readOnly
          variant="unstyled"
          autoFocus
          autosize
          mb="md"
          styles={{
            input: {
              border: 'none',
              fontSize: '16px',
            },
          }}
        />
      )}
    </Card>
  );
};

export default RootCauseAnalysis;
