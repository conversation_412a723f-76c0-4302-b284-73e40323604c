import { <PERSON>, Stack, Loader, Center } from '@mantine/core';
import { useParams } from 'react-router';
import { Suspense, lazy } from 'react';
import { queryIncidentMetrics } from '../../../hooks/useApi';
import { timelineData } from './mockData';
import ReportHeader from './ReportHeader';
import { useIncidentStatus } from '../../../contexts/IncidentStatusContext';

// Lazy load report sub-components
const ActionItems = lazy(() => import('./ActionItems'));
const ExecutiveSummary = lazy(() => import('./ExecutiveSummary'));
const IncidentTimeline = lazy(() => import('./IncidentTimeline'));
const PostIncidentActions = lazy(() => import('./PostIncidentActions'));
const ResponseMetricsTable = lazy(() => import('./ResponseMetricsTable'));
const Retrospectives = lazy(() => import('./Retrospectives'));
const RootCauseAnalysis = lazy(() => import('./RootCauseAnalysis'));
const ImpactAssessment = lazy(() => import('./ImpactAssessment'));

const IncidentReport = () => {
  const { incidentId } = useParams<{ incidentId: string }>();

  // Handle missing incidentId
  if (!incidentId) {
    return (
      <Paper p="lg" radius="md" withBorder>
        <Stack align="center" gap="md">
          <div>Error: Incident ID is missing</div>
          <div>Please select a valid incident.</div>
        </Stack>
      </Paper>
    );
  }

  // Fetch incident metrics from API
  const { data: metric } = queryIncidentMetrics(incidentId);

  const { approved } = useIncidentStatus();

  // Loading fallback component
  const LoadingFallback = () => (
    <Center p="md">
      <Loader size="sm" />
    </Center>
  );

  return (
    <Stack gap="lg">
      <Paper p="lg" radius="md" withBorder>
        <ReportHeader incidentId={incidentId} />

        <Stack gap="md">
          <Suspense fallback={<LoadingFallback />}>
            <ExecutiveSummary />
          </Suspense>
          <Suspense fallback={<LoadingFallback />}>
            <IncidentTimeline timelineData={timelineData} />
          </Suspense>
          <Suspense fallback={<LoadingFallback />}>
            <RootCauseAnalysis />
          </Suspense>
          <Suspense fallback={<LoadingFallback />}>
            <ResponseMetricsTable metric={metric || null} />
          </Suspense>
          <Suspense fallback={<LoadingFallback />}>
            <ImpactAssessment />
          </Suspense>
          <Suspense fallback={<LoadingFallback />}>
            <Retrospectives />
          </Suspense>
          <Suspense fallback={<LoadingFallback />}>
            <ActionItems />
          </Suspense>
          {!approved && (
            <Suspense fallback={<LoadingFallback />}>
              <PostIncidentActions />
            </Suspense>
          )}
        </Stack>
      </Paper>
    </Stack>
  );
};

export default IncidentReport;
