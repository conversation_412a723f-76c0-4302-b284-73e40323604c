import { Paper, Stack } from '@mantine/core';
import { useParams } from 'react-router';
import { queryIncidentMetrics } from '../../../hooks/useApi';
import ActionItems from './ActionItems';
import ExecutiveSummary from './ExecutiveSummary';
import IncidentTimeline from './IncidentTimeline';
import { timelineData } from './mockData';
import PostIncidentActions from './PostIncidentActions';
import ReportHeader from './ReportHeader';
import ResponseMetricsTable from './ResponseMetricsTable';
import Retrospectives from './Retrospectives';
import RootCauseAnalysis from './RootCauseAnalysis';
import ImpactAssessment from './ImpactAssessment';
import { useIncidentStatus } from '../../../contexts/IncidentStatusContext';

const IncidentReport = () => {
  const { incidentId } = useParams<{ incidentId: string }>();

  // Handle missing incidentId
  if (!incidentId) {
    return (
      <Paper p="lg" radius="md" withBorder>
        <Stack align="center" gap="md">
          <div>Error: Incident ID is missing</div>
          <div>Please select a valid incident.</div>
        </Stack>
      </Paper>
    );
  }

  // Fetch incident metrics from API
  const { data: metric } = queryIncidentMetrics(incidentId);

  const { approved } = useIncidentStatus();
  return (
    <Stack gap="lg">
      <Paper p="lg" radius="md" withBorder>
        <ReportHeader incidentId={incidentId} />

        <Stack gap="md">
          <ExecutiveSummary />
          <IncidentTimeline timelineData={timelineData} />
          <RootCauseAnalysis />
          <ResponseMetricsTable metric={metric || null} />
          <ImpactAssessment />
          <Retrospectives />
          <ActionItems />
          {!approved && <PostIncidentActions />}
        </Stack>
      </Paper>
    </Stack>
  );
};

export default IncidentReport;
