import { Card, Group, Text } from '@mantine/core';
import { ArrowRight } from 'lucide-react';

const PostIncidentActions = () => {
  return (
    <Card
      p="lg"
      bg="var(--color-primary)" //
      bd={10}
      onClick={() =>
        window.open('https://calendly.com/alfredo-3-1/30min', '_blank')
      }
      style={{
        transition: 'transform 0.2s',
      }}
      className="cursor-pointer hover:scale-[1.02] ease-in-out transition delay-150"
    >
      <Group align="center" justify="flex-end">
        <Text color="white" size="xl" fw={500}>
          Schedule a Meeting
        </Text>
        <ArrowRight color="white" />
      </Group>
    </Card>
  );
};

export default PostIncidentActions;
