import {
  Badge,
  Button,
  Group,
  Menu,
  Title,
  Modal,
  Textarea,
} from '@mantine/core';
import {
  <PERSON><PERSON>hart3,
  Circle<PERSON>heckBig,
  Download,
  FileText,
  Share,
  Sparkle,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { generateAndDownloadPdfReport } from '../../../api/reportApi';
import { useIncidentStatus } from '../../../contexts/IncidentStatusContext';
import { useDisclosure } from '@mantine/hooks';

interface ReportHeaderProps {
  incidentId: string;
}

const ReportHeader = ({ incidentId }: ReportHeaderProps) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const { approved, setApproved } = useIncidentStatus();
  const [opened, { open, close }] = useDisclosure(false);

  const handleExportPdf = async () => {
    setIsDownloading(true);
    try {
      await generateAndDownloadPdfReport(incidentId);
      toast.success('Incident report PDF has been downloaded successfully');
    } catch (error) {
      console.error('Failed to generate PDF:', error);
      toast.error('Failed to generate PDF report. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  const onGenerateReport = () => {
    open();
  };

  return (
    <>
      <Modal
        opened={opened}
        onClose={close}
        size="xl"
        title="Suggest Changes"
        centered
      >
        <Group justify="flex-end" gap="lg">
          <Textarea
            placeholder="Describe the changes you'd like to see in the report..."
            minRows={4}
            autosize
            w={'100%'}
            variant="filled"
            radius="md"
          />
          <Button
            variant="gradient"
            gradient={{ from: 'purple', to: 'blue', deg: 45 }}
            onClick={close}
          >
            Generate with AI
          </Button>
        </Group>
      </Modal>
      <Group justify="space-between" align="center" mb="md">
        <Group align="center" gap="sm">
          <BarChart3 size={24} color="purple" />
          <Title order={2}>Incident Report & Analysis</Title>
          <Badge
            gradient={{ from: 'purple', to: 'blue', deg: 45 }}
            leftSection={<Sparkle size={18} />}
            variant="gradient"
          >
            AI Generated Report
          </Badge>
        </Group>
        <Group gap="sm">
          <Button
            variant="light"
            color="green"
            disabled={approved}
            onClick={() => setApproved(true)}
            leftSection={<CircleCheckBig size={18} />}
          >
            {approved ? 'Approved' : 'Approve'}
          </Button>
          <Menu shadow="md" width={200}>
            <Menu.Target>
              <Button loading={isDownloading}>Actions</Button>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Item
                onClick={handleExportPdf}
                leftSection={<Download size={14} />}
              >
                Download Report
              </Menu.Item>
              <Menu.Item leftSection={<Share size={14} />}>
                Share Report
              </Menu.Item>
              <Menu.Item
                onClick={onGenerateReport}
                leftSection={<FileText size={14} />}
              >
                Regenerate Report
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      </Group>
    </>
  );
};

export default ReportHeader;
