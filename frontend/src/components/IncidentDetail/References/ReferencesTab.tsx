import {
  ActionIcon,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ge,
  Card,
  Group,
  Stack,
  Text,
  TextInput,
  ThemeIcon,
  Tooltip,
} from '@mantine/core';
import {
  AlertCircle,
  BookOpen,
  ExternalLink,
  FileText,
  Link,
  Search,
  X,
} from 'lucide-react';
import { useState } from 'react';
import { references } from './data';
const getTypeIcon = (type: string) => {
  switch (type) {
    case 'documentation':
      return <FileText size={16} />;
    case 'runbook':
      return <BookOpen size={16} />;
    case 'related_incident':
      return <AlertCircle size={16} />;
    case 'knowledge_base':
      return <Search size={16} />;
    case 'external':
      return <ExternalLink size={16} />;
    default:
      return <Link size={16} />;
  }
};

const getTypeColor = (type: string) => {
  switch (type) {
    case 'documentation':
      return 'blue';
    case 'runbook':
      return 'green';
    case 'related_incident':
      return 'orange';
    case 'knowledge_base':
      return 'purple';
    case 'external':
      return 'gray';
    default:
      return 'gray';
  }
};

const ReferencesTab = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredReferences = references.filter(
    ref =>
      ref.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ref.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ref.tags.some(tag =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
  );
  return (
    <>
      {/* Search */}
      <TextInput
        placeholder="Search references by title, description, or tags..."
        leftSection={<Search size={16} />}
        rightSection={
          searchQuery && (
            <Tooltip label="Clear search">
              <ActionIcon
                variant="subtle"
                color="gray"
                size="sm"
                onClick={() => setSearchQuery('')}
              >
                <X size={14} />
              </ActionIcon>
            </Tooltip>
          )
        }
        value={searchQuery}
        onChange={e => setSearchQuery(e.currentTarget.value)}
        mb="md"
      />

      {/* Auto-suggested references */}
      <Alert color="blue" icon={<AlertCircle size={16} />} mb="md">
        <Text size="sm">
          AI has automatically identified{' '}
          {references.filter(r => r.addedBy === 'System').length} relevant
          resources based on incident context and historical data.
        </Text>
      </Alert>

      {/* References list */}
      <Stack gap="md">
        {filteredReferences
          .sort((a, b) => b.relevanceScore - a.relevanceScore)
          .map(reference => (
            <Card key={reference.id} padding="md" radius="sm" withBorder>
              <Group justify="space-between" align="flex-start" mb="xs">
                <Group align="center" gap="sm">
                  <ThemeIcon
                    size="md"
                    color={getTypeColor(reference.type)}
                    variant="light"
                  >
                    {getTypeIcon(reference.type)}
                  </ThemeIcon>
                  <div style={{ flex: 1 }}>
                    <Group align="center" gap="xs" mb="xs">
                      <Anchor href={reference.url} target="_blank" fw={600}>
                        {reference.title}
                      </Anchor>
                      <ActionIcon
                        size="sm"
                        variant="subtle"
                        component="a"
                        href={reference.url}
                        target="_blank"
                      >
                        <ExternalLink size={14} />
                      </ActionIcon>
                    </Group>
                    <Text size="sm" c="dimmed" mb="xs">
                      {reference.description}
                    </Text>
                    <Group gap="xs">
                      {reference.tags.map(tag => (
                        <Badge key={tag} size="xs" variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </Group>
                  </div>
                </Group>
                <Stack align="flex-end" gap="xs">
                  <Badge
                    size="sm"
                    color={
                      reference.relevanceScore > 85
                        ? 'green'
                        : reference.relevanceScore > 70
                          ? 'orange'
                          : 'gray'
                    }
                    variant="light"
                  >
                    {reference.relevanceScore}% relevant
                  </Badge>
                  <Group gap="xs" align="center">
                    <Text size="xs" c="dimmed">
                      by {reference.addedBy}
                    </Text>
                    <Text size="xs" c="dimmed">
                      {new Date(reference.addedAt).toLocaleTimeString()}
                    </Text>
                  </Group>
                </Stack>
              </Group>
            </Card>
          ))}
      </Stack>
    </>
  );
};

export default ReferencesTab;
