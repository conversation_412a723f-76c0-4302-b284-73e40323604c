import {
  ActionIcon,
  Badge,
  Card,
  Group,
  Stack,
  Text,
  ThemeIcon,
} from '@mantine/core';
import { ExternalLink, FileText } from 'lucide-react';
import { attachments } from './data';
const AttachmentsTab = () => {
  return (
    <Stack gap="md">
      {attachments.map(attachment => (
        <Card key={attachment.id} padding="md" radius="sm" withBorder>
          <Group justify="space-between" align="flex-start">
            <Group align="center" gap="sm">
              <ThemeIcon size="lg" color="blue" variant="light">
                <FileText size={20} />
              </ThemeIcon>
              <div>
                <Group gap="xs" align="center">
                  <Text size="sm" mb="xs">
                    {attachment.name}
                  </Text>
                </Group>
                <Group gap="xs" align="center">
                  <Badge size="xs" variant="outline">
                    {attachment.type}
                  </Badge>
                  <Text size="xs" c="dimmed">
                    {attachment.size}
                  </Text>
                </Group>
                <Group gap="xs" align="center" mt="xs">
                  <Text size="xs" c="dimmed">
                    Added by {attachment.addedBy}
                  </Text>
                  <Text size="xs" c="dimmed">
                    {new Date(attachment.addedAt).toLocaleString()}
                  </Text>
                </Group>
              </div>
            </Group>
            <Group gap="xs">
              <ActionIcon size="sm" variant="subtle">
                <ExternalLink size={14} />
              </ActionIcon>
            </Group>
          </Group>
        </Card>
      ))}
    </Stack>
  );
};

export default AttachmentsTab;
