// Data exports for References components to avoid circular imports with lazy loading

interface Attachment {
  id: string;
  name: string;
  type: string;
  size: string;
  addedBy: string;
  addedAt: string;
}

export const attachments: Attachment[] = [
  {
    id: '1',
    name: 'Payment API Logs',
    type: 'log',
    size: '2.5MB',
    addedBy: '<PERSON> Biju',
    addedAt: '2025-05-13T06:15:00Z',
  },
  {
    id: '2',
    name: 'Payment API Traces',
    type: 'trace',
    size: '1.2MB',
    addedBy: '<PERSON>',
    addedAt: '2025-05-13T06:30:00Z',
  },
];

interface Discussion {
  id: string;
  author: string;
  content: string;
  timestamp: string;
  type: 'comment' | 'link' | 'insight';
}

export const discussions: Discussion[] = [
  {
    id: '1',
    author: '<PERSON> Biju',
    content:
      'Added documentation link for parameter validation. This should help with understanding the expected format.',
    timestamp: '2025-05-13T06:15:00Z',
    type: 'link',
  },
  {
    id: '2',
    author: '<PERSON>',
    content:
      'Found similar incident from last month. The resolution involved updating the validation middleware.',
    timestamp: '2025-05-13T06:30:00Z',
    type: 'insight',
  },
  {
    id: '3',
    author: 'System',
    content:
      'Automatically linked related runbook based on incident tags and affected services.',
    timestamp: '2025-05-13T06:45:00Z',
    type: 'comment',
  },
];

interface Reference {
  id: string;
  type: 'documentation' | 'runbook' | 'monitoring' | 'external' | 'related_incident' | 'knowledge_base';
  title: string;
  description: string;
  url: string;
  tags: string[];
  addedBy: string;
  addedAt: string;
  relevanceScore: number;
}

export const references: Reference[] = [
  {
    id: '1',
    type: 'documentation',
    title: 'Payment API Documentation',
    description:
      'Complete API documentation for the payment service including parameter validation rules',
    url: 'https://docs.company.com/payment-api',
    tags: ['api', 'payment', 'parameters'],
    addedBy: 'System',
    addedAt: '2025-05-13T06:00:00Z',
    relevanceScore: 95,
  },
  {
    id: '2',
    type: 'runbook',
    title: 'Payment Service Rollback Procedure',
    description:
      'Step-by-step guide for rolling back payment service deployments',
    url: 'https://runbooks.company.com/payment-rollback',
    tags: ['rollback', 'deployment', 'payment'],
    addedBy: 'Alan Biju',
    addedAt: '2025-05-13T06:15:00Z',
    relevanceScore: 88,
  },
  {
    id: '3',
    type: 'related_incident',
    title: 'INC-3821 - Similar Payment API Issue',
    description:
      'Previous incident with parameter validation failure, resolved by updating validation logic',
    url: '/incidents/3821',
    tags: ['payment', 'validation', 'resolved'],
    addedBy: 'System',
    addedAt: '2025-05-13T05:55:00Z',
    relevanceScore: 92,
  },
  {
    id: '4',
    type: 'knowledge_base',
    title: 'Parameter Validation Best Practices',
    description:
      'Guidelines for implementing robust parameter validation in microservices',
    url: 'https://kb.company.com/validation-best-practices',
    tags: ['validation', 'best-practices', 'microservices'],
    addedBy: 'Ron Jose',
    addedAt: '2025-05-13T06:30:00Z',
    relevanceScore: 78,
  },
  {
    id: '5',
    type: 'external',
    title: 'HTTP 400 Bad Request Troubleshooting',
    description:
      'External resource on debugging and fixing HTTP 400 errors in REST APIs',
    url: 'https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400',
    tags: ['http', 'troubleshooting', 'external'],
    addedBy: 'Alan Biju',
    addedAt: '2025-05-13T07:00:00Z',
    relevanceScore: 65,
  },
];
