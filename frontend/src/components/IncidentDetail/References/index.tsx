import { Badge, Button, Group, Paper, Stack, Tabs, Title, Loader, Center } from '@mantine/core';
import { FileText, MessageSquare, Plus } from 'lucide-react';
import { useState, Suspense, lazy } from 'react';

// Import the data exports directly for the badge counts
import { attachments } from './AttachmentsTab';
import { discussions } from './DiscussionsTab';
import { references } from './ReferencesTab';

// Lazy load reference sub-components
const AttachmentsTab = lazy(() => import('./AttachmentsTab'));
const DiscussionsTab = lazy(() => import('./DiscussionsTab'));
const ReferencesTab = lazy(() => import('./ReferencesTab'));

const IncidentReferences = () => {
  const [activeTab, setActiveTab] = useState<string | null>('references');

  // Loading fallback component
  const LoadingFallback = () => (
    <Center p="md">
      <Loader size="sm" />
    </Center>
  );

  return (
    <Stack gap="lg">
      <Paper p="lg" radius="md" withBorder>
        <Group justify="space-between" align="center" mb="md">
          <Group align="center" gap="sm">
            <FileText size={24} color="blue" />
            <Title order={2}>References & Resources</Title>
            <Badge color="blue" variant="light">
              {references.length} resources
            </Badge>
          </Group>
          <Button leftSection={<Plus size={16} />} size="sm">
            Add Reference
          </Button>
        </Group>

        <Tabs value={activeTab} onChange={setActiveTab} color="blue">
          <Tabs.List>
            <Tabs.Tab value="references" leftSection={<FileText size={16} />}>
              References ({references.length})
            </Tabs.Tab>
            <Tabs.Tab
              value="attachments"
              leftSection={<MessageSquare size={16} />}
            >
              Attachments ({attachments.length})
            </Tabs.Tab>
            <Tabs.Tab
              value="discussions"
              leftSection={<MessageSquare size={16} />}
            >
              Discussion ({discussions.length})
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="references" pt="md">
            <Suspense fallback={<LoadingFallback />}>
              <ReferencesTab />
            </Suspense>
          </Tabs.Panel>

          <Tabs.Panel value="discussions" pt="md">
            <Suspense fallback={<LoadingFallback />}>
              <DiscussionsTab />
            </Suspense>
          </Tabs.Panel>
          <Tabs.Panel value="attachments" pt="md">
            <Suspense fallback={<LoadingFallback />}>
              <AttachmentsTab />
            </Suspense>
          </Tabs.Panel>
        </Tabs>
      </Paper>
    </Stack>
  );
};

export default IncidentReferences;
