import {
  <PERSON><PERSON>con,
  Badge,
  Card,
  Divider,
  Group,
  Text,
  TextInput,
  ThemeIcon,
  Timeline,
} from '@mantine/core';
import { AlertCircle, Link, MessageSquare, Plus } from 'lucide-react';
import { discussions } from './data';

const getDiscussionIcon = (type: string) => {
  switch (type) {
    case 'link':
      return <Link size={16} />;
    case 'insight':
      return <AlertCircle size={16} />;
    case 'comment':
      return <MessageSquare size={16} />;
    default:
      return <MessageSquare size={16} />;
  }
};

const DiscussionsTab = () => {
  return (
    <>
      <Timeline active={discussions.length} bulletSize={24}>
        {discussions.map(discussion => (
          <Timeline.Item
            key={discussion.id}
            bullet={
              <ThemeIcon size={24} radius="xl" color="blue" variant="light">
                {getDiscussionIcon(discussion.type)}
              </ThemeIcon>
            }
            title={
              <Group gap="xs" align="center">
                <Text size="sm" fw={500}>
                  {discussion.author}
                </Text>
                <Badge size="xs" variant="outline">
                  {discussion.type}
                </Badge>
              </Group>
            }
          >
            <Text size="sm" mb="xs">
              {discussion.content}
            </Text>
            <Text size="xs" c="dimmed">
              {new Date(discussion.timestamp).toLocaleString()}
            </Text>
          </Timeline.Item>
        ))}
      </Timeline>

      <Divider my="md" />

      <Card padding="md" withBorder>
        <Text size="sm" fw={500} mb="xs">
          Add to Discussion
        </Text>
        <TextInput
          placeholder="Share a reference, insight, or comment..."
          rightSection={
            <ActionIcon>
              <Plus size={16} />
            </ActionIcon>
          }
        />
      </Card>
    </>
  );
};

export default DiscussionsTab;
