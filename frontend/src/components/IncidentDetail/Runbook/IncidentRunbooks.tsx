import {
  ActionIcon,
  Alert,
  Badge,
  Button,
  Group,
  Paper,
  Stack,
  Tabs,
  Text,
  TextInput,
  ThemeIcon,
  Timeline,
  Title,
  Tooltip,
  Loader,
  Center,
} from '@mantine/core';
import {
  AlertTriangle,
  BookOpen,
  Calendar,
  Plus,
  Search,
  X,
} from 'lucide-react';
import { useDisclosure } from '@mantine/hooks';
import { useState, Suspense, lazy } from 'react';
import { queryRunbooks } from '../../../hooks/useApi';

// Lazy load runbook sub-components
const CreateRunbookModal = lazy(() => import('./CreateRunbookModal'));
const RunbookCard = lazy(() => import('./RunbookCard'));

const IncidentRunbooks = ({ incidentId }: { incidentId: string }) => {
  const [activeRecommendedRunbook, setActiveRecommendedRunbook] = useState<
    string | null
  >(null);

  const [activeRunbook, setActiveRunbook] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const { data: runbooks } = queryRunbooks(incidentId);
  const [opened, { open, close }] = useDisclosure(false);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'rollback':
        return 'red';
      case 'investigation':
        return 'blue';
      case 'mitigation':
        return 'orange';
      case 'recovery':
        return 'green';
      default:
        return 'gray';
    }
  };

  const getRecommendedRunbooks = () => {
    return runbooks?.filter(rb => rb) ?? [];
  };

  const recommendedRunbooks = getRecommendedRunbooks();

  // Loading fallback component
  const LoadingFallback = () => (
    <Center p="sm">
      <Loader size="xs" />
    </Center>
  );

  return (
    <>
      <Stack gap="lg">
        <Paper p="lg" radius="md" withBorder>
          <Group justify="space-between" align="center" mb="md">
            <Group align="center" gap="sm">
              <BookOpen size={24} color="green" />
              <Title order={2}>Runbooks & Procedures</Title>
              <Badge color="green" variant="light">
                {runbooks?.length} available
              </Badge>
            </Group>
            <Button
              leftSection={<Plus size={16} />}
              size="sm"
              variant="filled"
              onClick={open}
            >
              Create Runbook
            </Button>
          </Group>

          {/* AI Recommendations */}
          {recommendedRunbooks.length > 0 && (
            <Alert color="green" icon={<AlertTriangle size={16} />} mb="md">
              <Text size="sm">
                <strong>AI Recommendation:</strong> Based on my analysis of the
                incident and the available runbooks, I recommend starting with
                the {recommendedRunbooks[0].title} runbook.
              </Text>
            </Alert>
          )}

          <Tabs defaultValue="recommended" color="green">
            <Tabs.List>
              <Tabs.Tab value="recommended">
                Recommended ({recommendedRunbooks.length})
              </Tabs.Tab>
              <Tabs.Tab value="all">All Runbooks ({runbooks?.length})</Tabs.Tab>
              <Tabs.Tab value="history">Execution History</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="recommended" pt="md">
              <Stack gap="md">
                {recommendedRunbooks.map(runbook => (
                  <Suspense key={runbook.id} fallback={<LoadingFallback />}>
                    <RunbookCard
                      runbook={runbook}
                      incidentId={incidentId}
                      getCategoryColor={getCategoryColor}
                      isActive={activeRecommendedRunbook === runbook.id}
                      setActive={runbookId =>
                        setActiveRecommendedRunbook(runbookId)
                      }
                    />
                  </Suspense>
                ))}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="all" pt="md">
              <TextInput
                placeholder="Search runbooks by title, keywords, or tags..."
                leftSection={<Search size={16} />}
                rightSection={
                  searchQuery && (
                    <Tooltip label="Clear search">
                      <ActionIcon
                        variant="subtle"
                        color="gray"
                        size="sm"
                        onClick={() => setSearchQuery('')}
                      >
                        <X size={14} />
                      </ActionIcon>
                    </Tooltip>
                  )
                }
                value={searchQuery}
                onChange={e => setSearchQuery(e.currentTarget.value)}
                mb="md"
              />
              <Stack gap="md">
                {runbooks
                  ?.filter(runbook => {
                    if (!searchQuery.trim()) return true;
                    const query = searchQuery.toLowerCase();
                    return (
                      runbook.title.toLowerCase().includes(query) ||
                      runbook.purpose.toLowerCase().includes(query) ||
                      runbook.type.toLowerCase().includes(query)
                    );
                  })
                  ?.map(runbook => (
                    <Suspense key={runbook.id} fallback={<LoadingFallback />}>
                      <RunbookCard
                        runbook={runbook}
                        incidentId={incidentId}
                        getCategoryColor={getCategoryColor}
                        isActive={activeRunbook === runbook.id}
                        setActive={runbookId => setActiveRunbook(runbookId)}
                      />
                    </Suspense>
                  ))}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="history" pt="md">
              <Timeline active={-1} bulletSize={24}>
                <Timeline.Item
                  bullet={
                    <ThemeIcon size={24} radius="xl" color="blue">
                      <Calendar size={14} />
                    </ThemeIcon>
                  }
                  title="No execution history"
                >
                  <Text size="sm" c="dimmed">
                    No runbooks have been executed for this incident yet.
                  </Text>
                </Timeline.Item>
              </Timeline>
            </Tabs.Panel>
          </Tabs>
        </Paper>
      </Stack>
      <CreateRunbookModal
        opened={opened}
        onClose={close}
        incidentId={incidentId}
      />
    </>
  );
};

export default IncidentRunbooks;
