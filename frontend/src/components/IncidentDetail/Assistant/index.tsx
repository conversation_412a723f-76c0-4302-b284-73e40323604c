import { Stack, Loader, Center } from '@mantine/core';
import { Suspense, lazy } from 'react';

// Lazy load assistant sub-components
const AnalysisSummary = lazy(() => import('./AnalysisSummary'));
const SuggestedActions = lazy(() => import('./SuggestedActions'));

const IncidentAIAssistant = () => {
  // Loading fallback component
  const LoadingFallback = () => (
    <Center p="md">
      <Loader size="sm" />
    </Center>
  );

  return (
    <Stack gap="lg">
      <Suspense fallback={<LoadingFallback />}>
        <AnalysisSummary />
      </Suspense>
      <Suspense fallback={<LoadingFallback />}>
        <SuggestedActions />
      </Suspense>
    </Stack>
  );
};

export default IncidentAIAssistant;
