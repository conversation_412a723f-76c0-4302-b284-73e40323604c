import React, { useState, useCallback, useEffect } from 'react';
import { useHotkeys } from '@mantine/hooks';
import {
  Modal,
  Text,
  Group,
  ActionIcon,
  Button,
  TextInput,
  Stack,
  Box,
  Paper,
  ScrollArea,
} from '@mantine/core';
import { motion, AnimatePresence } from 'motion/react';
import {
  MessageSquare,
  Bot,
  Mic,
  Send,
  X,
  ExternalLink,
  Square,
} from 'lucide-react';
import { useNavigate } from 'react-router';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';
import {
  useChat,
  messageVariants,
  overlayVariants,
  getEventIcon,
} from '../utils';

import { ChatMessage } from '../types/Agent';

interface SpotlightChatModalProps {
  opened: boolean;
  onClose: () => void;
}

const SpotlightChatModal: React.FC<SpotlightChatModalProps> = ({
  opened,
  onClose,
}) => {
  const navigate = useNavigate();
  const [currentOverlayContent, setCurrentOverlayContent] =
    useState<ChatMessage | null>(null);
  const [showInput, setShowInput] = useState(true);
  const [showRespondButton, setShowRespondButton] = useState(false);
  const [showFullConversation, setShowFullConversation] = useState(false);

  // Custom overlay update function to show events
  const handleOverlayUpdate = useCallback((content: ChatMessage | null) => {
    if (content && content.type === 'event') {
      setCurrentOverlayContent(content);
    } else {
      // Clear overlay for non-event messages or when explicitly set to null
      setCurrentOverlayContent(null);
    }
  }, []);

  // Use the real chat functionality
  const {
    messages,
    currentInput,
    setCurrentInput,
    chatState,
    sendMessage,
    stopProcessing,
    clearChat,
    handleVoiceInput,
    session,
  } = useChat({
    appName: 'incident-management',
    agentName: 'coordinator',
    userId: 'current-user',
    onOverlayUpdate: handleOverlayUpdate,
  });

  // Update UI state based on chat state
  useEffect(() => {
    if (chatState.isProcessing) {
      setShowInput(false);
      setShowRespondButton(false);
    } else if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage?.type === 'ai') {
        setShowRespondButton(true);
        setShowInput(false);
      }
    } else {
      setShowInput(true);
      setShowRespondButton(false);
    }
  }, [chatState.isProcessing, messages]);

  // Clear overlay when showing full conversation
  useEffect(() => {
    if (showFullConversation) {
      setCurrentOverlayContent(null);
    }
  }, [showFullConversation]);

  const handleSendMessage = useCallback(() => {
    if (!currentInput.trim() || chatState.isProcessing) return;
    sendMessage();
  }, [currentInput, chatState.isProcessing, sendMessage]);

  const handleKeyPress = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSendMessage();
      }
    },
    [handleSendMessage],
  );

  const handleMicClick = () => {
    handleVoiceInput();
  };

  const handleClearChat = () => {
    clearChat();
    setCurrentOverlayContent(null);
    setShowInput(true);
    setShowRespondButton(false);
    setShowFullConversation(false);
  };

  const handleStop = () => {
    stopProcessing();
    setCurrentOverlayContent(null);
    setShowInput(true);
    setShowRespondButton(false);
  };

  const handleRespond = () => {
    setShowRespondButton(false);
    setShowInput(true);
    setShowFullConversation(false);
  };

  const handleGoToFullChat = () => {
    const sessionId = session?.id;
    onClose();
    if (sessionId) {
      navigate(`/chat?sessionId=${sessionId}`);
    } else {
      navigate('/chat');
    }
  };

  // Determine messages to show
  const messagesToRender = showFullConversation
    ? messages // In full conversation, show all messages including events in chronological order
    : (() => {
        // When not showing full conversation, show recent context excluding events (events shown as overlay)
        const userMessages = messages.filter(msg => msg.type === 'user');
        const lastUserMessage = userMessages[userMessages.length - 1];

        if (!lastUserMessage) return [];

        // Find the index of the last user message
        const lastUserMessageIndex = messages.findIndex(
          msg => msg.id === lastUserMessage.id,
        );

        // Get all messages from the last user message onwards, excluding events
        const recentMessages = messages
          .slice(lastUserMessageIndex)
          .filter(msg => {
            // Include user and ai messages, but exclude events (they're shown in overlay)
            return msg.type === 'user' || msg.type === 'ai';
          });

        return recentMessages;
      })();

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      size="lg"
      centered
      withCloseButton={false}
      padding={0}
      styles={{
        content: {
          borderRadius: '16px',
          overflow: 'hidden',
        },
        body: {
          padding: 0,
        },
      }}
    >
      <style>
        {`
          @keyframes pulse {
            0%, 100% {
              opacity: 1;
            }
            50% {
              opacity: 0.5;
            }
          }
        `}
      </style>
      <Box style={{ position: 'relative' }}>
        {/* Header */}
        <Paper
          p="md"
          style={{ borderBottom: '1px solid var(--mantine-color-gray-2)' }}
        >
          <Group justify="space-between" align="center">
            <Group align="center" gap="sm">
              <Bot size={20} style={{ color: 'var(--color-primary)' }} />
              <Text fw={600} c="var(--color-primary)">
                AI Assistant
              </Text>
            </Group>

            <Group gap="xs">
              {!chatState.isProcessing &&
                messages.length > 0 &&
                (showInput || showRespondButton) && (
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    onClick={() => setShowFullConversation(prev => !prev)}
                    title="Toggle conversation history"
                  >
                    {getEventIcon('long_running_operation', { size: 16 })}
                  </ActionIcon>
                )}
              <ActionIcon
                variant="subtle"
                color="gray"
                onClick={handleGoToFullChat}
                title="Open full chat"
              >
                <ExternalLink size={16} />
              </ActionIcon>
              <ActionIcon variant="subtle" color="gray" onClick={onClose}>
                <X size={16} />
              </ActionIcon>
            </Group>
          </Group>
        </Paper>

        {/* Messages Area */}
        <ScrollArea h={300} style={{ position: 'relative' }}>
          <Box p="md" style={{ minHeight: '268px' }}>
            <Stack
              gap="sm"
              style={{
                justifyContent: !showFullConversation ? 'center' : 'flex-start',
                minHeight: !showFullConversation ? '200px' : 'auto',
              }}
            >
              <AnimatePresence mode="popLayout">
                {messagesToRender
                  .map(msg => {
                    let messageStyle: React.CSSProperties = {
                      padding: '12px 16px',
                      borderRadius: '12px',
                      maxWidth: '85%',
                    };
                    let content = (
                      <Box
                        style={{
                          fontSize: '14px',
                          lineHeight: '1.5',
                        }}
                      >
                        <ReactMarkdown
                          remarkPlugins={[remarkGfm, remarkBreaks]}
                          rehypePlugins={[
                            rehypeHighlight,
                            rehypeRaw,
                            rehypeSanitize,
                          ]}
                          components={{
                            p: ({ children }) => (
                              <Text
                                size="sm"
                                style={{
                                  margin: '0 0 8px 0',
                                  whiteSpace: 'pre-wrap',
                                  wordBreak: 'break-word',
                                  overflowWrap: 'break-word',
                                }}
                              >
                                {children}
                              </Text>
                            ),
                            code: ({ children, className }) => {
                              const isInline =
                                !className?.includes('language-');

                              if (isInline) {
                                return (
                                  <code
                                    style={{
                                      fontFamily:
                                        'var(--mantine-font-family-monospace)',
                                      fontSize: '13px',
                                      backgroundColor:
                                        'rgba(255, 255, 255, 0.2)',
                                      color: 'inherit',
                                      padding: '2px 4px',
                                      borderRadius: '3px',
                                      whiteSpace: 'nowrap',
                                    }}
                                  >
                                    {children}
                                  </code>
                                );
                              }

                              // For code blocks, let hljs handle the styling
                              return (
                                <pre
                                  className={className}
                                  style={{ margin: '8px 0' }}
                                >
                                  <code className={className}>{children}</code>
                                </pre>
                              );
                            },
                            blockquote: ({ children }) => (
                              <Box
                                style={{
                                  borderLeft:
                                    '3px solid rgba(255, 255, 255, 0.3)',
                                  paddingLeft: '12px',
                                  margin: '8px 0',
                                  fontStyle: 'italic',
                                }}
                              >
                                {children}
                              </Box>
                            ),
                            ul: ({ children }) => (
                              <Box
                                component="ul"
                                style={{ margin: '8px 0', paddingLeft: '16px' }}
                              >
                                {children}
                              </Box>
                            ),
                            ol: ({ children }) => (
                              <Box
                                component="ol"
                                style={{ margin: '8px 0', paddingLeft: '16px' }}
                              >
                                {children}
                              </Box>
                            ),
                            li: ({ children }) => (
                              <Text
                                component="li"
                                size="sm"
                                style={{ margin: '2px 0' }}
                              >
                                {children}
                              </Text>
                            ),
                            strong: ({ children }) => (
                              <Text component="strong" fw={700}>
                                {children}
                              </Text>
                            ),
                            em: ({ children }) => (
                              <Text
                                component="em"
                                style={{ fontStyle: 'italic' }}
                              >
                                {children}
                              </Text>
                            ),
                            a: ({ children, href }) => (
                              <Text
                                component="a"
                                href={href}
                                style={{
                                  color: 'inherit',
                                  textDecoration: 'underline',
                                  opacity: 0.9,
                                }}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                {children}
                              </Text>
                            ),
                          }}
                        >
                          {msg.content}
                        </ReactMarkdown>
                      </Box>
                    );

                    if (msg.type === 'ai') {
                      messageStyle = {
                        ...messageStyle,
                        backgroundColor: 'var(--color-primary)',
                        color: 'white',
                        alignSelf: !showFullConversation
                          ? 'center'
                          : 'flex-start',
                      };
                    } else if (msg.type === 'user') {
                      messageStyle = {
                        ...messageStyle,
                        backgroundColor: 'var(--mantine-color-gray-1)',
                        color: 'var(--mantine-color-gray-8)',
                        alignSelf: 'flex-end',
                      };
                    } else if (msg.type === 'event') {
                      // Render event messages in full conversation view
                      if (showFullConversation) {
                        content = (
                          <Group
                            gap="xs"
                            align="center"
                            style={{ justifyContent: 'center' }}
                          >
                            {getEventIcon(msg.eventType, {
                              size: 16,
                              style: { color: 'var(--color-secondary)' },
                            })}
                            <Text size="xs" c="dimmed">
                              {msg.content}
                            </Text>
                          </Group>
                        );
                        messageStyle = {
                          ...messageStyle,
                          alignSelf: 'center',
                          maxWidth: '90%',
                          padding: '4px 0px',
                        };
                      } else {
                        // Don't render event messages in compact view (they're shown in overlay)
                        return null;
                      }
                    }

                    return (
                      <motion.div
                        key={msg.id}
                        variants={messageVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        layout
                        transition={{
                          layout: { duration: 0.2, ease: 'easeInOut' },
                          ...messageVariants.visible.transition,
                        }}
                        style={messageStyle}
                      >
                        <motion.div
                          layout
                          transition={{ duration: 0.15, ease: 'easeInOut' }}
                        >
                          {content}
                        </motion.div>
                      </motion.div>
                    );
                  })
                  .filter(Boolean)}
              </AnimatePresence>
            </Stack>
          </Box>

          {/* Processing Overlay */}
          <AnimatePresence>
            {(chatState.isProcessing || currentOverlayContent) && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(255, 255, 255, 0.95)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '16px',
                  zIndex: 10,
                }}
              >
                <AnimatePresence mode="wait">
                  {currentOverlayContent ? (
                    <motion.div
                      key={currentOverlayContent.id}
                      variants={overlayVariants}
                      initial="hidden"
                      animate="visible"
                      exit="exit"
                      style={{
                        textAlign: 'center',
                        padding: '16px',
                        borderRadius: '12px',
                        backgroundColor: 'white',
                        border: '1px solid var(--mantine-color-gray-2)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                      }}
                    >
                      <Group gap="xs" align="center" justify="center">
                        {getEventIcon(currentOverlayContent.eventType)}
                        <Text c="var(--color-primary)" fw={500}>
                          {currentOverlayContent.content}
                        </Text>
                      </Group>
                    </motion.div>
                  ) : chatState.isProcessing ? (
                    <motion.div
                      key="processing"
                      variants={overlayVariants}
                      initial="hidden"
                      animate="visible"
                      exit="exit"
                      style={{
                        textAlign: 'center',
                        padding: '16px',
                        borderRadius: '12px',
                        backgroundColor: 'white',
                        border: '1px solid var(--mantine-color-gray-2)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                      }}
                    >
                      <Text c="var(--color-primary)" fw={500}>
                        Thinking...
                      </Text>
                    </motion.div>
                  ) : null}
                </AnimatePresence>

                {chatState.isProcessing && (
                  <Button
                    variant="subtle"
                    color="gray"
                    leftSection={<Square size={16} />}
                    onClick={handleStop}
                  >
                    Stop
                  </Button>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </ScrollArea>

        {/* Input Area */}
        <Paper
          p="md"
          style={{
            borderTop: '1px solid var(--mantine-color-gray-2)',
            backgroundColor: 'var(--mantine-color-gray-0)',
          }}
        >
          <AnimatePresence mode="wait">
            {showInput && (
              <motion.div
                key="input-area"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.2 }}
              >
                <Group gap="sm">
                  <TextInput
                    placeholder="Ask your AI assistant..."
                    value={currentInput}
                    onChange={e => setCurrentInput(e.target.value)}
                    onKeyDown={handleKeyPress}
                    style={{ flex: 1 }}
                    disabled={chatState.isProcessing}
                    data-autofocus
                  />
                  <ActionIcon
                    size="lg"
                    onClick={handleSendMessage}
                    disabled={!currentInput.trim() || chatState.isProcessing}
                    style={{
                      backgroundColor: currentInput.trim()
                        ? 'var(--color-primary)'
                        : undefined,
                      color: currentInput.trim() ? 'white' : undefined,
                    }}
                  >
                    <Send size={18} />
                  </ActionIcon>
                  <ActionIcon
                    size="lg"
                    onClick={handleMicClick}
                    disabled={chatState.isProcessing}
                    style={{
                      backgroundColor: 'var(--color-secondary)',
                      color: 'white',
                    }}
                  >
                    <Mic size={18} />
                  </ActionIcon>
                </Group>

                {messages.length > 0 && (
                  <Group justify="center" mt="sm">
                    <Button
                      variant="subtle"
                      size="xs"
                      onClick={handleClearChat}
                      disabled={chatState.isProcessing}
                    >
                      Clear Chat
                    </Button>
                  </Group>
                )}
              </motion.div>
            )}

            {showRespondButton && (
              <motion.div
                key="respond-button"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.2 }}
              >
                <Button
                  fullWidth
                  variant="subtle"
                  onClick={handleRespond}
                  leftSection={<MessageSquare size={16} />}
                >
                  Respond
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </Paper>
      </Box>
    </Modal>
  );
};

// Global Chat Provider Component
export const GlobalSpotlightChat: React.FC = () => {
  const [chatModalOpened, setChatModalOpened] = useState(false);

  // Register global hotkey for Cmd+K to open spotlight
  useHotkeys([
    [
      'mod+K',
      () => {
        if (chatModalOpened) {
          setChatModalOpened(false);
        } else {
          setChatModalOpened(true);
        }
      },
    ],
  ]);

  return (
    <SpotlightChatModal
      opened={chatModalOpened}
      onClose={() => setChatModalOpened(false)}
    />
  );
};

export default GlobalSpotlightChat;
