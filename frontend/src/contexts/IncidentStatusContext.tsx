import { createContext, useContext, useState, ReactNode } from 'react';

interface IncidentStatusContextProps {
  approved: boolean;
  setApproved: (approved: boolean) => void;
}

const IncidentStatusContext = createContext<
  IncidentStatusContextProps | undefined
>(undefined);

export const IncidentStatusProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [approved, setApproved] = useState<boolean>(false);

  return (
    <IncidentStatusContext.Provider value={{ approved, setApproved }}>
      {children}
    </IncidentStatusContext.Provider>
  );
};

export const useIncidentStatus = () => {
  const context = useContext(IncidentStatusContext);
  if (!context) {
    throw new Error(
      'useIncidentStatus must be used within an IncidentStatusProvider',
    );
  }
  return context;
};
