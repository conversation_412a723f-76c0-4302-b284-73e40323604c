import {
  AgentRunRequest,
  Session,
  AgentEvent,
  ChatMessage,
  EventType,
} from '../types/Agent';
import { config } from '../utils/configService';

const API_BASE_URL = config.getApiUrl();

export class AgentService {
  private static instance: AgentService;
  private websocket: WebSocket | null = null;

  static getInstance(): AgentService {
    if (!AgentService.instance) {
      AgentService.instance = new AgentService();
    }
    return AgentService.instance;
  }

  // Session management
  async createSession(
    appName: string,
    userId: string,
    sessionId?: string,
  ): Promise<Session> {
    // If sessionId is provided, try to get existing session first
    if (sessionId) {
      try {
        return await this.getSession(appName, userId, sessionId);
      } catch (error) {
        // If session doesn't exist, we'll create it below
        if (
          error instanceof Error &&
          error.message.includes('Failed to get session')
        ) {
          // Fall through to create session
        } else {
          throw error;
        }
      }
    }

    const url = sessionId
      ? `${API_BASE_URL}/agents/apps/${appName}/users/${userId}/sessions/${sessionId}`
      : `${API_BASE_URL}/agents/apps/${appName}/users/${userId}/sessions`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    });

    if (!response.ok) {
      throw new Error(`Failed to create session: ${response.statusText}`);
    }

    return response.json();
  }

  async listSessions(appName: string, userId: string): Promise<Session[]> {
    const response = await fetch(
      `${API_BASE_URL}/agents/apps/${appName}/users/${userId}/sessions`,
    );

    if (!response.ok) {
      throw new Error(`Failed to list sessions: ${response.statusText}`);
    }

    return response.json();
  }

  async getSession(
    appName: string,
    userId: string,
    sessionId: string,
  ): Promise<Session> {
    const response = await fetch(
      `${API_BASE_URL}/agents/apps/${appName}/users/${userId}/sessions/${sessionId}`,
    );

    if (!response.ok) {
      throw new Error(`Failed to get session: ${response.statusText}`);
    }

    return response.json();
  }

  async deleteSession(
    appName: string,
    userId: string,
    sessionId: string,
  ): Promise<void> {
    const response = await fetch(
      `${API_BASE_URL}/agents/apps/${appName}/users/${userId}/sessions/${sessionId}`,
      { method: 'DELETE' },
    );

    if (!response.ok) {
      throw new Error(`Failed to delete session: ${response.statusText}`);
    }
  }

  // Regular agent run
  async runAgent(request: AgentRunRequest): Promise<AgentEvent[]> {
    const response = await fetch(`${API_BASE_URL}/agents/run`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`Agent run failed: ${response.statusText}`);
    }

    return response.json();
  }

  // Streaming SSE agent run
  runAgentSSE(
    request: AgentRunRequest,
    onEvent: (event: AgentEvent) => void,
    onError: (error: Error) => void,
  ): () => void {
    let controller = new AbortController();

    // Make the streaming request
    fetch(`${API_BASE_URL}/agents/run_sse`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'text/event-stream',
      },
      body: JSON.stringify({ ...request, streaming: true }),
      signal: controller.signal,
    })
      .then(async response => {
        if (!response.ok) {
          throw new Error(`SSE request failed: ${response.statusText}`);
        }

        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('Response body is not readable');
        }

        const decoder = new TextDecoder('utf-8');
        let buffer = '';

        const read = async () => {
          try {
            const { done, value } = await reader.read();

            if (done) {
              return;
            }

            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            try {
              // Process complete SSE messages
              const lines = buffer
                .split(/\r?\n/)
                .filter(line => line.trim().startsWith('data:'));

              lines.forEach(line => {
                const data = line.replace(/^data:\s*/, '').trim();

                // Skip empty data lines
                if (!data) return;

                // Handle error responses
                if (data.startsWith('{"error"') || data.includes('"error"')) {
                  console.error('SSE Error Response:', data);
                  onError(new Error(data));
                  return;
                }

                try {
                  const eventData = JSON.parse(data);

                  // Handle error in parsed data
                  if (eventData.error) {
                    console.error('Parsed SSE Error:', eventData.error);
                    onError(new Error(eventData.error));
                    return;
                  }

                  // Handle validation errors specifically
                  if (eventData.detail && Array.isArray(eventData.detail)) {
                    const validationError = eventData.detail
                      .map(
                        (err: any) =>
                          `${err.loc?.join('.')}: ${err.msg} (input: ${JSON.stringify(err.input)})`,
                      )
                      .join('; ');
                    console.error('Validation Error:', validationError);
                    onError(new Error(`Validation Error: ${validationError}`));
                    return;
                  }

                  // Convert backend event format to our AgentEvent format
                  const agentEvent: AgentEvent = {
                    id: eventData.id || `event-${Date.now()}-${Math.random()}`,
                    type: eventData.type || 'agent_response',
                    data: eventData,
                    timestamp: eventData.timestamp || Date.now(),
                  };

                  onEvent(agentEvent);
                } catch (parseError) {
                  console.warn('Failed to parse SSE event:', data, parseError);
                }
              });

              // Clear the buffer after processing complete lines
              buffer = '';
            } catch (e) {
              // If parsing fails, it might be an incomplete chunk, continue reading
              if (!(e instanceof SyntaxError)) {
                throw e;
              }
            }

            // Continue reading next chunk
            read();
          } catch (error) {
            if (controller.signal.aborted) {
              return; // Request was cancelled
            }
            throw error;
          }
        };

        read();
      })
      .catch(error => {
        if (!controller.signal.aborted) {
          onError(error);
        }
      });

    // Return cleanup function
    return () => {
      controller.abort();
    };
  }

  // WebSocket live agent run
  runAgentLive(
    appName: string,
    agentName: string,
    userId: string,
    sessionId: string,
    onEvent: (event: AgentEvent) => void,
    onError: (error: Error) => void,
    onClose: () => void,
    modalities: ('TEXT' | 'AUDIO')[] = ['TEXT', 'AUDIO'],
  ): () => void {
    this.closeWebSocketConnection();

    const wsUrl = new URL(
      `${API_BASE_URL.replace('http', 'ws')}/agents/run_live`,
    );
    wsUrl.searchParams.set('app_name', appName);
    wsUrl.searchParams.set('agent_name', agentName);
    wsUrl.searchParams.set('user_id', userId);
    wsUrl.searchParams.set('session_id', sessionId);
    modalities.forEach(modality =>
      wsUrl.searchParams.append('modalities', modality),
    );

    this.websocket = new WebSocket(wsUrl.toString());

    this.websocket.onopen = () => {
      console.log('WebSocket connection established');
    };

    this.websocket.onmessage = event => {
      try {
        const data = JSON.parse(event.data);
        onEvent(data);
      } catch (error) {
        onError(new Error(`Failed to parse WebSocket message: ${error}`));
      }
    };

    this.websocket.onerror = error => {
      onError(new Error(`WebSocket error: ${error}`));
    };

    this.websocket.onclose = event => {
      if (event.code !== 1000) {
        onError(
          new Error(
            `WebSocket closed with code ${event.code}: ${event.reason}`,
          ),
        );
      }
      onClose();
    };

    // Return cleanup function
    return () => this.closeWebSocketConnection();
  }

  // Send message through WebSocket
  sendLiveMessage(content: any, blob?: any, modelConfig?: any): void {
    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not connected');
    }

    const message = {
      content,
      blob,
      model_config: modelConfig,
    };

    this.websocket.send(JSON.stringify(message));
  }

  // Close WebSocket connection
  closeLiveConnection(): void {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({ close: true }));
    }
    this.closeWebSocketConnection();
  }

  private closeWebSocketConnection(): void {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
  }

  // Utility method to convert events to chat messages
  static eventToChatMessage(event: AgentEvent): ChatMessage {
    // Extract text content from the event
    let content = '';
    let eventType: EventType | undefined;

    if (event.data.content && event.data.content.parts) {
      // Process all parts and combine content
      const textParts: string[] = [];
      const functionCalls: string[] = [];
      const functionResponses: string[] = [];
      const inlineDataParts: string[] = [];
      const codeParts: string[] = [];
      const codeResults: string[] = [];

      event.data.content.parts.forEach((part: any) => {
        if (part.text) {
          if (part.thought) {
            textParts.push(
              `💭 ${part.text.replace('/*PLANNING*/', '').replace('/*ACTION*/', '')}`,
            );
            eventType = 'thought';
          } else {
            textParts.push(part.text);
          }
        } else if (part.functionCall || part.function_call) {
          const fc = part.functionCall || part.function_call;
          functionCalls.push(`🔧 ${fc.name}(${JSON.stringify(fc.args || {})})`);
          eventType = 'tool_call';
        } else if (part.functionResponse || part.function_response) {
          const fr = part.functionResponse || part.function_response;
          functionResponses.push(
            `✅ ${fr.name}: ${JSON.stringify(fr.response || {})}`,
          );
          eventType = 'tool_response';
        } else if (part.inlineData) {
          inlineDataParts.push(
            `📎 ${part.inlineData.displayName || 'File attachment'}`,
          );
          eventType = 'inline_data';
        } else if (part.executableCode) {
          codeParts.push(
            `\`\`\`${part.executableCode.language || 'code'}\n${part.executableCode.code}\n\`\`\``,
          );
          eventType = 'executable_code';
        } else if (part.codeExecutionResult) {
          codeResults.push(
            `▶️ Code result: ${part.codeExecutionResult.output || 'Executed successfully'}`,
          );
          eventType = 'code_execution_result';
        }
      });

      // Combine all content types
      const allContent = [
        ...textParts,
        ...functionCalls,
        ...functionResponses,
        ...inlineDataParts,
        ...codeParts,
        ...codeResults,
      ];

      content = allContent.join('\n') || 'Processing...';
    } else if (event.data.errorMessage) {
      content = `❌ Error: ${event.data.errorMessage}`;
      eventType = 'error';
    } else {
      content = JSON.stringify(event.data);
    }

    // Handle special event types
    if (
      event.data.longRunningToolIds &&
      event.data.longRunningToolIds.length > 0
    ) {
      content = '⏳ Long running operation in progress...';
      eventType = 'long_running_operation';
    }

    if (event.data.actions?.artifactDelta) {
      const artifactIds = Object.keys(event.data.actions.artifactDelta);
      content = `📦 Artifacts updated: ${artifactIds.join(', ')}`;
      eventType = 'artifact_update';
    }

    if (event.data.evalStatus) {
      content = `📊 Evaluation: ${event.data.evalStatus}`;
      eventType = 'evaluation';
    }

    // Determine message type based on author or event type
    let messageType: 'user' | 'ai' | 'system' | 'event' = 'event';

    if (event.data.author === 'user') {
      messageType = 'user';
    } else if (event.data.author && event.data.author !== 'user') {
      messageType = 'ai';
    } else if (event.type === 'final_response' || event.data.content) {
      messageType = 'ai';
    }

    // Override message type for certain event types
    if (
      [
        'tool_call',
        'tool_response',
        'agent_transfer',
        'inline_data',
        'executable_code',
        'code_execution_result',
        'thought',
        'long_running_operation',
        'artifact_update',
        'evaluation',
        'error',
      ].includes(eventType || '')
    ) {
      messageType = 'event';
    }

    return {
      id: event.id,
      type: messageType,
      content: content.trim(),
      timestamp: event.timestamp,
      eventType,
      metadata: {
        ...event.data,
        renderedContent:
          event.data.groundingMetadata?.searchEntryPoint?.renderedContent,
      },
    };
  }
}

export const agentService = AgentService.getInstance();
