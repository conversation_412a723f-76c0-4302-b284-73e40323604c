import { Ta<PERSON>, <PERSON><PERSON>, Center } from '@mantine/core';
import { Activity, BarChart3, BookOpen, Bot, FileText } from 'lucide-react';
import { useState, Suspense, lazy } from 'react';
import { useParams } from 'react-router';

import IncidentDetailLayout from '../components/IncidentDetail/IncidentDetailLayout';
import { IncidentStatusProvider } from '../contexts/IncidentStatusContext';

// Lazy load all incident detail components
const IncidentOverview = lazy(() => import('../components/IncidentDetail/Overview'));
const IncidentAIAssistant = lazy(() => import('../components/IncidentDetail/Assistant'));
const IncidentReferences = lazy(() => import('../components/IncidentDetail/References'));
const IncidentRunbooks = lazy(() => import('../components/IncidentDetail/Runbook/IncidentRunbooks'));
const IncidentReport = lazy(() => import('../components/IncidentDetail/Report/IncidentReport'));

const IncidentDetail = () => {
  const { incidentId } = useParams<{ incidentId: string }>();
  const [activeTab, setActiveTab] = useState<string | null>('overview');

  const getTabIcon = (tab: string) => {
    switch (tab) {
      case 'overview':
        return <Activity size={16} />;
      case 'ai-assistant':
        return <Bot size={16} />;
      case 'references':
        return <FileText size={16} />;
      case 'runbooks':
        return <BookOpen size={16} />;
      case 'report':
        return <BarChart3 size={16} />;
      default:
        return null;
    }
  };

  const getTabStyle = (tab: string) => {
    return {
      padding: '16px',
      borderRadius: '10px',
      border: '1px solid var(--mantine-color-gray-3)',
      backgroundColor:
        activeTab === tab ? undefined : 'var(--mantine-color-body) ',
      color: activeTab === tab ? undefined : 'black',
    };
  };

  // Loading fallback component
  const LoadingFallback = () => (
    <Center p="xl">
      <Loader size="md" />
    </Center>
  );

  if (!incidentId) {
    return null;
  }

  return (
    <IncidentDetailLayout incidentId={incidentId}>
      <Tabs
        value={activeTab}
        onChange={setActiveTab}
        color="var(--color-primary)"
        variant="pills"
      >
        <Tabs.List grow>
          <Tabs.Tab
            value="overview"
            leftSection={getTabIcon('overview')}
            style={getTabStyle('overview')}
          >
            Overview
          </Tabs.Tab>
          <Tabs.Tab
            value="ai-assistant"
            leftSection={getTabIcon('ai-assistant')}
            style={getTabStyle('ai-assistant')}
          >
            AI Assistant
          </Tabs.Tab>
          <Tabs.Tab
            value="references"
            leftSection={getTabIcon('references')}
            style={getTabStyle('references')}
          >
            References
          </Tabs.Tab>
          <Tabs.Tab
            value="runbooks"
            leftSection={getTabIcon('runbooks')}
            style={getTabStyle('runbooks')}
          >
            Runbooks
          </Tabs.Tab>
          <Tabs.Tab
            value="report"
            leftSection={getTabIcon('report')}
            style={getTabStyle('report')}
          >
            Report
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          <Suspense fallback={<LoadingFallback />}>
            <IncidentOverview />
          </Suspense>
        </Tabs.Panel>

        <Tabs.Panel value="ai-assistant" pt="md">
          <Suspense fallback={<LoadingFallback />}>
            <IncidentAIAssistant />
          </Suspense>
        </Tabs.Panel>

        <Tabs.Panel value="references" pt="md">
          <Suspense fallback={<LoadingFallback />}>
            <IncidentReferences />
          </Suspense>
        </Tabs.Panel>

        <Tabs.Panel value="runbooks" pt="md">
          <Suspense fallback={<LoadingFallback />}>
            <IncidentRunbooks incidentId={incidentId} />
          </Suspense>
        </Tabs.Panel>

        <Tabs.Panel value="report" pt="md">
          <Suspense fallback={<LoadingFallback />}>
            <IncidentStatusProvider>
              <IncidentReport />
            </IncidentStatusProvider>
          </Suspense>
        </Tabs.Panel>
      </Tabs>
    </IncidentDetailLayout>
  );
};

export default IncidentDetail;
