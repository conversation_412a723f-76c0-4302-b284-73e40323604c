import React from 'react';
import { Box } from '@mantine/core';
import { useSearchParams } from 'react-router';
import AgentChatInterface from '../components/AgentChatInterface';

// Agent Chat page integrated with the incident management app
const AgentChatPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const sessionId = searchParams.get('sessionId');

  return (
    <Box
      style={{
        height: '85vh',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: 'var(--mantine-color-gray-0)',
      }}
    >
      <Box style={{ flex: 1, overflow: 'hidden', minHeight: 0 }}>
        <AgentChatInterface
          appName="incident-management"
          agentName="coordinator"
          userId="current-user"
          sessionId={sessionId || undefined}
          enableLiveMode={false}
        />
      </Box>
    </Box>
  );
};

export default AgentChatPage;
