import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@mantine/charts';
import {
  Badge,
  Card,
  Container,
  Grid,
  Group,
  Progress,
  SimpleGrid,
  Stack,
  Text,
  ThemeIcon,
  Title,
  rem,
} from '@mantine/core';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Timer,
  TrendingUp,
  Zap,
} from 'lucide-react';
import { useMemo } from 'react';
import {
  calculateMetrics,
  generateIncidentTimeData,
  generateMockIncidents,
} from '../utils/metrics';

export default function Dashboard() {
  // Using mock data for demo - replace with actual data fetching
  const incidents = useMemo(() => generateMockIncidents(), []);
  const metrics = useMemo(() => calculateMetrics(incidents), [incidents]);
  const timeData = useMemo(
    () => generateIncidentTimeData(incidents),
    [incidents],
  );

  const metricCards = [
    {
      title: 'MTBF',
      value: `${metrics.mtbf}h`,
      description: 'Mean Time Between Failures',
      icon: TrendingUp,
      color: 'blue',
      detail: 'Average time between repairable failures of a tech product',
    },
    {
      title: 'MTTD',
      value: `${metrics.mttd}m`,
      description: 'Mean Time To Detect',
      icon: Target,
      color: 'orange',
      detail: 'Average time it takes your team to discover an issue',
    },
    {
      title: 'MTTA',
      value: `${metrics.mtta}m`,
      description: 'Mean Time To Acknowledge',
      icon: Clock,
      color: 'yellow',
      detail: 'Average time between alert and team acknowledgment',
    },
    {
      title: 'MTTR',
      value: `${metrics.mttr}h`,
      description: 'Mean Time To Resolve',
      icon: Zap,
      color: 'green',
      detail: 'Average time to repair/resolve/respond/recovery',
    },
  ];

  const statusCards = [
    {
      title: 'Total Incidents',
      value: metrics.totalIncidents,
      icon: Activity,
      color: 'gray',
    },
    {
      title: 'Open Incidents',
      value: metrics.openIncidents,
      icon: AlertTriangle,
      color: 'red',
    },
    {
      title: 'Resolved',
      value: metrics.resolvedIncidents,
      icon: CheckCircle,
      color: 'green',
    },
    {
      title: 'Critical',
      value: metrics.criticalIncidents,
      icon: AlertTriangle,
      color: 'red',
    },
  ];

  const severityData = [
    {
      severity: 'critical',
      count: incidents.filter(i => i.severity === 'critical').length,
      color: '#ff6b6b',
    },
    {
      severity: 'high',
      count: incidents.filter(i => i.severity === 'high').length,
      color: '#ffa726',
    },
    {
      severity: 'medium',
      count: incidents.filter(i => i.severity === 'medium').length,
      color: '#42a5f5',
    },
    {
      severity: 'low',
      count: incidents.filter(i => i.severity === 'low').length,
      color: '#66bb6a',
    },
  ];

  return (
    <Container fluid className="w-full">
      <Stack gap="xl">
        <Group justify="space-between" align="center">
          <Title order={1} size={rem(36)} fw={600} c="var(--color-primary)">
            Incident Management Dashboard
          </Title>
          <Badge size="lg" variant="light" color="blue">
            Last updated: {new Date().toLocaleTimeString()}
          </Badge>
        </Group>

        {/* Key Metrics Cards */}
        <div>
          <Title order={2} size={rem(24)} mb="md" c="var(--color-primary)">
            Key Performance Metrics
          </Title>
          <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md">
            {metricCards.map(metric => (
              <Card
                key={metric.title}
                shadow="sm"
                padding="lg"
                radius="md"
                withBorder
              >
                <Group justify="space-between" mb="xs">
                  <ThemeIcon
                    size={40}
                    radius="md"
                    variant="light"
                    color={metric.color}
                  >
                    <metric.icon size={24} />
                  </ThemeIcon>
                  <Text size="xl" fw={700} c={`${metric.color}.6`}>
                    {metric.value}
                  </Text>
                </Group>
                <Text fw={500} size="sm" c="dark.6" mb="xs">
                  {metric.title} - {metric.description}
                </Text>
                <Text size="xs" c="dimmed">
                  {metric.detail}
                </Text>
              </Card>
            ))}
          </SimpleGrid>
        </div>

        {/* Status Overview */}
        <div>
          <Title order={2} size={rem(24)} mb="md" c="var(--color-primary)">
            Incident Overview
          </Title>
          <SimpleGrid cols={{ base: 2, sm: 4 }} spacing="md">
            {statusCards.map(status => (
              <Card
                key={status.title}
                shadow="sm"
                padding="lg"
                radius="md"
                withBorder
              >
                <Group justify="space-between" align="center">
                  <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                      {status.title}
                    </Text>
                    <Text fw={700} size="xl">
                      {status.value}
                    </Text>
                  </div>
                  <ThemeIcon
                    size={38}
                    radius="md"
                    variant="light"
                    color={status.color}
                  >
                    <status.icon size={20} />
                  </ThemeIcon>
                </Group>
              </Card>
            ))}
          </SimpleGrid>
        </div>

        {/* Charts Grid */}
        <Grid>
          {/* Incidents Over Time */}
          <Grid.Col span={{ base: 12, lg: 8 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder h="400">
              <Title order={3} size={rem(20)} mb="md" c="var(--color-primary)">
                Incidents Over Time (Last 30 Days)
              </Title>
              <AreaChart
                h={300}
                data={timeData}
                dataKey="date"
                series={[
                  {
                    name: 'incidents',
                    label: 'Total Incidents',
                    color: 'blue.6',
                  },
                  { name: 'resolved', label: 'Resolved', color: 'green.6' },
                  { name: 'critical', label: 'Critical', color: 'red.6' },
                ]}
                curveType="monotone"
                tickLine="xy"
                gridAxis="xy"
                withXAxis
                withYAxis
                withLegend
                withTooltip={false}
              />
            </Card>
          </Grid.Col>

          {/* Incidents by Severity */}
          <Grid.Col span={{ base: 12, lg: 4 }}>
            <Card shadow="sm" padding="lg" radius="md" withBorder h="400">
              <Title order={3} size={rem(20)} mb="md" c="var(--color-primary)">
                Incidents by Severity
              </Title>
              <BarChart
                h={300}
                data={severityData}
                dataKey="severity"
                series={[{ name: 'count' }]}
                tickLine="y"
                withXAxis
                withYAxis
                withTooltip={false}
              />
            </Card>
          </Grid.Col>
        </Grid>

        {/* Resolution Performance */}
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Title order={3} size={rem(20)} mb="md" c="var(--color-primary)">
            Resolution Performance
          </Title>
          <SimpleGrid cols={{ base: 1, md: 2 }} spacing="xl">
            <Stack gap="md">
              <div>
                <Group justify="space-between" mb="xs">
                  <Text size="sm" fw={500}>
                    Resolution Rate
                  </Text>
                  <Text size="sm" c="dimmed">
                    {Math.round(
                      (metrics.resolvedIncidents / metrics.totalIncidents) *
                        100,
                    )}
                    %
                  </Text>
                </Group>
                <Progress
                  value={
                    (metrics.resolvedIncidents / metrics.totalIncidents) * 100
                  }
                  color="green"
                  size="lg"
                  radius="md"
                />
              </div>

              <div>
                <Group justify="space-between" mb="xs">
                  <Text size="sm" fw={500}>
                    Critical Incident Rate
                  </Text>
                  <Text size="sm" c="dimmed">
                    {Math.round(
                      (metrics.criticalIncidents / metrics.totalIncidents) *
                        100,
                    )}
                    %
                  </Text>
                </Group>
                <Progress
                  value={
                    (metrics.criticalIncidents / metrics.totalIncidents) * 100
                  }
                  color="red"
                  size="lg"
                  radius="md"
                />
              </div>
            </Stack>

            <Stack gap="md">
              <div
                style={{
                  padding: '16px',
                  backgroundColor: 'var(--mantine-color-gray-0)',
                  borderRadius: '8px',
                }}
              >
                <Group justify="space-between">
                  <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                      Avg Response Time
                    </Text>
                    <Text fw={700} size="lg">
                      {metrics.mtta} minutes
                    </Text>
                  </div>
                  <ThemeIcon size={40} radius="md" variant="light" color="blue">
                    <Timer size={20} />
                  </ThemeIcon>
                </Group>
              </div>

              <div
                style={{
                  padding: '16px',
                  backgroundColor: 'var(--mantine-color-gray-0)',
                  borderRadius: '8px',
                }}
              >
                <Group justify="space-between">
                  <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                      Avg Resolution Time
                    </Text>
                    <Text fw={700} size="lg">
                      {metrics.mttr} hours
                    </Text>
                  </div>
                  <ThemeIcon
                    size={40}
                    radius="md"
                    variant="light"
                    color="green"
                  >
                    <CheckCircle size={20} />
                  </ThemeIcon>
                </Group>
              </div>
            </Stack>
          </SimpleGrid>
        </Card>
      </Stack>
    </Container>
  );
}
