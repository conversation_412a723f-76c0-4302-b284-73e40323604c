import {
  Check,
  Database,
  ArrowRight,
  File,
  Code,
  Play,
  Brain,
  Clock,
  Package,
  Target,
  AlertTriangle,
  AlertCircle,
} from 'lucide-react';
import { EventType } from '../types/Agent';

/**
 * Get icon component for event types
 */
export const getEventIcon = (eventType?: EventType, iconProps?: any) => {
  const defaultProps = {
    size: 16,
    style: { color: 'var(--color-secondary)' },
    ...iconProps,
  };

  switch (eventType) {
    case 'tool_call':
      return <Database {...defaultProps} />;
    case 'tool_response':
      return <Check {...defaultProps} />;
    case 'agent_transfer':
      return <ArrowRight {...defaultProps} />;
    case 'inline_data':
      return <File {...defaultProps} />;
    case 'executable_code':
      return <Code {...defaultProps} />;
    case 'code_execution_result':
      return <Play {...defaultProps} />;
    case 'thought':
      return <Brain {...defaultProps} />;
    case 'long_running_operation':
      return <Clock {...defaultProps} />;
    case 'artifact_update':
      return <Package {...defaultProps} />;
    case 'evaluation':
      return <Target {...defaultProps} />;
    case 'error':
      return <AlertTriangle {...defaultProps} />;
    default:
      return <AlertCircle {...defaultProps} />;
  }
};
