/**
 * File handling utilities for chat file uploads
 */

// Read file as bytes (base64)
export const readFileAsBytes = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      const base64Data = e.target.result.split(',')[1];
      resolve(base64Data);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

// Accepted MIME types for file uploads
export const acceptedMimeTypes = [
  'application/pdf',
  'audio/mpeg',
  'audio/mp3',
  'audio/wav',
  'image/png',
  'image/jpeg',
  'image/webp',
  'text/plain',
  'video/mov',
  'video/mpeg',
  'video/mp4',
  'video/mpg',
  'video/avi',
  'video/wmv',
  'video/mpegps',
  'video/flv',
];

// Validate file type
export const validateFileType = (file: File): boolean => {
  return acceptedMimeTypes.includes(file.type);
};

// Normalize MIME type to ensure it matches our accepted list
export const normalizeMimeType = (mimeType: string): string => {
  const normalizedType = mimeType.toLowerCase();

  // Map common variations to standard MIME types
  if (normalizedType === 'image/jpg') return 'image/jpeg';
  if (normalizedType === 'audio/mp3') return 'audio/mpeg';

  // Return the type if it's in our accepted list, otherwise return the original
  return acceptedMimeTypes.includes(normalizedType) ? normalizedType : mimeType;
};

// Create user message parts from input and files
export const createUserMessageParts = async (
  currentInput: string,
  selectedFiles: { file: File; url: string }[],
): Promise<any[]> => {
  const parts: any[] = [];

  if (currentInput.trim()) {
    parts.push({ text: currentInput });
  }

  if (selectedFiles.length > 0) {
    for (const file of selectedFiles) {
      parts.push({
        inlineData: {
          displayName: file.file.name,
          data: await readFileAsBytes(file.file),
          mimeType: normalizeMimeType(file.file.type),
        },
      });
    }
  }

  return parts;
};

export interface FileUploadResult {
  newFiles: { file: File; url: string }[];
  rejectedFiles: string[];
}

// Handle file selection from input change event
export const handleFileSelect = (
  event: React.ChangeEvent<HTMLInputElement>,
  onError?: (message: string) => void,
): FileUploadResult => {
  const input = event.target;
  const result: FileUploadResult = {
    newFiles: [],
    rejectedFiles: [],
  };

  if (input.files) {
    for (let i = 0; i < input.files.length; i++) {
      const file = input.files[i];

      // Validate file type
      if (validateFileType(file)) {
        const url = URL.createObjectURL(file);
        result.newFiles.push({ file, url });
      } else {
        result.rejectedFiles.push(
          `${file.name} (${file.type || 'unknown type'})`,
        );
      }
    }

    // Show error for rejected files
    if (result.rejectedFiles.length > 0 && onError) {
      onError(
        `Unsupported file types: ${result.rejectedFiles.join(', ')}. Please select files with supported formats.`,
      );
    }
  }

  // Clear input value to allow re-selecting the same file
  input.value = '';

  return result;
};

// Remove file from selected files list
export const removeFile = (
  index: number,
  selectedFiles: { file: File; url: string }[],
): { file: File; url: string }[] => {
  const fileToRemove = selectedFiles[index];
  if (fileToRemove) {
    URL.revokeObjectURL(fileToRemove.url);
  }
  return selectedFiles.filter((_, i) => i !== index);
};

// Get accept attribute string for file input
export const getFileInputAccept = (): string => {
  return acceptedMimeTypes.join(',');
};

// Format file size for display
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
