import { useState, useCallback, useRef, useEffect } from 'react';
import {
  ChatMessage,
  ChatState,
  Session,
  AgentRunRequest,
  AgentEvent,
} from '../types/Agent';
import { agentService } from '../services/agentService';
import { createUserMessage } from './chatUtils';
import { createUserMessageParts } from './fileUtils';
import {
  processSessionEvents,
  createErrorMessage,
  processAgentEvent,
} from './eventProcessing';

interface UseChatOptions {
  appName: string;
  agentName: string;
  userId: string;
  sessionId?: string;
  enableLiveMode?: boolean;
  initialSessionState?: any;
  onOverlayUpdate?: (content: ChatMessage | null) => void;
}

export const useChat = (options: UseChatOptions) => {
  const {
    appName,
    agentName,
    userId,
    sessionId: initialSessionId,
    enableLiveMode = false,
    initialSessionState,
    onOverlayUpdate = () => {},
  } = options;

  // State
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<
    { file: File; url: string }[]
  >([]);
  const [chatState, setChatState] = useState<ChatState>({
    isProcessing: false,
    isConnected: false,
  });
  const [session, setSession] = useState<Session | null>(null);
  const [updatedSessionState, setUpdatedSessionState] = useState<any>(
    initialSessionState || null,
  );

  // Refs
  const cleanupRef = useRef<(() => void) | null>(null);

  // Create session context
  const sessionContext = session?.id
    ? {
        appName,
        agentName,
        userId,
        sessionId: session.id,
      }
    : undefined;

  // Event handlers
  const handleProcessingComplete = useCallback(() => {
    setChatState(prev => ({ ...prev, isProcessing: false }));
    onOverlayUpdate(null);
  }, [onOverlayUpdate]);

  const handleAgentEvent = useCallback(
    (event: AgentEvent): void => {
      if (event.data && event.data.content && event.data.content.parts) {
        // Process event data (both content parts and special actions)
        processAgentEvent(
          event.data,
          setMessages,
          onOverlayUpdate,
          handleProcessingComplete,
          sessionContext,
        );
      } else if (event.data?.errorMessage || event.data?.error) {
        // Handle errors
        const errorText = event.data.errorMessage || event.data.error;
        const errorMessage = createErrorMessage(errorText);
        setMessages(prev => [...prev, errorMessage]);
        setChatState(prev => ({ ...prev, isProcessing: false }));
        onOverlayUpdate(null);
      }
    },
    [sessionContext, onOverlayUpdate, handleProcessingComplete],
  );

  const handleError = useCallback(
    (error: Error): void => {
      console.error('Agent error:', error);
      setChatState(prev => ({
        ...prev,
        isProcessing: false,
        error: error.message,
      }));
      onOverlayUpdate(null);
    },
    [onOverlayUpdate],
  );

  // Initialize session
  useEffect(() => {
    const initSession = async () => {
      try {
        let newSession: Session;

        if (initialSessionId) {
          try {
            newSession = await agentService.getSession(
              appName,
              userId,
              initialSessionId,
            );
            // Load existing messages if any
            if (newSession.events?.length > 0) {
              const existingMessages = processSessionEvents(newSession.events);
              setMessages(existingMessages);
            }
          } catch {
            // Create new session if existing one fails
            newSession = await agentService.createSession(appName, userId);
          }
        } else {
          // Create new session
          newSession = await agentService.createSession(
            appName,
            userId,
            initialSessionId,
          );
        }

        setSession(newSession);
      } catch (error) {
        console.error('Failed to initialize session:', error);
        setChatState(prev => ({
          ...prev,
          error: 'Failed to initialize session',
        }));
      }
    };

    initSession();
  }, [appName, userId, initialSessionId]);

  // Cleanup connections on unmount
  useEffect(() => {
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
      }
    };
  }, []);

  // Send message function
  const sendMessage = useCallback(async () => {
    if (!currentInput.trim() && selectedFiles.length === 0) return;
    if (!session?.id || chatState.isProcessing) return;

    // Create user messages
    const userMessages = createUserMessage(currentInput, selectedFiles);
    setMessages(prev => [...prev, ...userMessages]);

    const request: AgentRunRequest = {
      appName,
      agentName,
      userId,
      sessionId: session.id,
      newMessage: {
        role: 'user',
        parts: await createUserMessageParts(currentInput, selectedFiles),
      },
      streaming: true,
      ...(updatedSessionState && { stateDelta: updatedSessionState }),
    };

    // Clear input and files
    setCurrentInput('');
    selectedFiles.forEach(file => URL.revokeObjectURL(file.url));
    setSelectedFiles([]);
    setUpdatedSessionState(null);
    setChatState(prev => ({ ...prev, isProcessing: true, error: undefined }));

    try {
      if (enableLiveMode) {
        cleanupRef.current = agentService.runAgentLive(
          appName,
          agentName,
          userId,
          session.id,
          handleAgentEvent,
          handleError,
          () => setChatState(prev => ({ ...prev, isConnected: false })),
        );
        setChatState(prev => ({ ...prev, isConnected: true }));
      } else {
        cleanupRef.current = agentService.runAgentSSE(
          request,
          handleAgentEvent,
          handleError,
        );
        setChatState(prev => ({ ...prev, isConnected: true }));
      }
    } catch (error) {
      handleError(error as Error);
    }
  }, [
    currentInput,
    selectedFiles,
    session,
    chatState.isProcessing,
    appName,
    agentName,
    userId,
    enableLiveMode,
    updatedSessionState,
    handleAgentEvent,
    handleError,
  ]);

  // Control functions
  const stopProcessing = useCallback(() => {
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }
    setChatState({ isProcessing: false, isConnected: false });
  }, []);

  const clearChat = useCallback(() => {
    setMessages([]);
    setCurrentInput('');
    setChatState(prev => ({ ...prev, error: undefined }));
  }, []);

  const handleVoiceInput = useCallback(() => {
    const voiceMessage: ChatMessage = {
      id: `voice-${Date.now()}`,
      type: 'user',
      content: 'Voice input detected...',
      timestamp: Date.now(),
    };
    setMessages(prev => [...prev, voiceMessage]);
  }, []);

  return {
    // State
    messages,
    currentInput,
    setCurrentInput,
    selectedFiles,
    setSelectedFiles,
    chatState,
    session,

    // Actions
    sendMessage,
    stopProcessing,
    clearChat,
    handleVoiceInput,
  };
};
