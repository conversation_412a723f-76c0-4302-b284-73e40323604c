import { ChatMessage } from '../types/Agent';

/**
 * Utility functions for chat message processing and formatting
 */

// Process thought text by removing planning markers
export const processThoughtText = (text: string): string => {
  return text.replace('/*PLANNING*/', '').replace('/*ACTION*/', '');
};

// Format base64 data with proper MIME type
export const formatBase64Data = (data: string, mimeType: string): string => {
  // Fix base64 padding
  let fixedBase64Data = data;
  while (fixedBase64Data.length % 4 !== 0) {
    fixedBase64Data += '=';
  }
  return `data:${mimeType};base64,${fixedBase64Data}`;
};

// Create a chat message from user input
export const createUserMessage = (
  input: string,
  attachments?: { file: File; url: string }[],
): ChatMessage[] => {
  const messages: ChatMessage[] = [];

  // Main user message
  const userMessage: ChatMessage = {
    id: `user-${Date.now()}`,
    type: 'user',
    content: input,
    timestamp: Date.now(),
  };
  messages.push(userMessage);

  // Attachment message if files are present
  if (attachments && attachments.length > 0) {
    const attachmentMessage: ChatMessage = {
      id: `user-attachments-${Date.now()}`,
      type: 'user',
      content: `📎 ${attachments.length} file(s) attached`,
      timestamp: Date.now(),
      metadata: {
        attachments: attachments.map(file => ({
          file: file.file,
          url: file.url,
        })),
      },
    };
    messages.push(attachmentMessage);
  }

  return messages;
};

// Animation variants for message animations
export const messageVariants = {
  hidden: { opacity: 0, y: 20, scale: 0.95 },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: { type: 'spring' as const, stiffness: 200, damping: 20 },
  },
  exit: { opacity: 0, y: -20, scale: 0.9, transition: { duration: 0.2 } },
} as const;

// Animation variants for overlay content
export const overlayVariants = {
  hidden: { opacity: 0, scale: 0.9, y: 10 },
  visible: { opacity: 1, scale: 1, y: 0 },
  exit: { opacity: 0, scale: 0.9, y: -10 },
} as const;
