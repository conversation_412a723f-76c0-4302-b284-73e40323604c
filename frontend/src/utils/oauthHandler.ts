/**
 * OAuth and Long Running Operations Handler
 * This utility handles OAuth flows and long-running operations
 */

import { AgentRunRequest } from '../types/Agent';
import { agentService } from '../services/agentService';

export interface OAuthConfig {
  exchangedAuthCredential: {
    oauth2: {
      authUri: string;
      authResponseUri?: string;
    };
  };
}

export interface LongRunningOperation {
  id: string;
  name: string;
  args: {
    authConfig?: OAuthConfig;
  };
}

export class OAuthHandler {
  private longRunningEvents: LongRunningOperation[] = [];
  private functionCallEventId: string = '';

  /**
   * Handle OAuth popup flow
   */
  async handleOAuthFlow(
    func: LongRunningOperation,
    appName: string,
    agentName: string,
    userId: string,
    sessionId: string,
    onAuthComplete?: () => void,
  ): Promise<void> {
    if (!func?.args?.authConfig?.exchangedAuthCredential?.oauth2) {
      console.error('Invalid OAuth config');
      return;
    }

    const authUri = func.args.authConfig.exchangedAuthCredential.oauth2.authUri;
    const redirectUri = window.location.origin;

    // Update redirect URI
    let updatedAuthUri = authUri;
    try {
      const url = new URL(authUri);
      url.searchParams.set('redirect_uri', redirectUri);
      updatedAuthUri = url.toString();
    } catch (error) {
      console.warn('Failed to update redirect URI: ', error);
    }

    // Open OAuth popup
    const popup = window.open(
      updatedAuthUri,
      'oauthPopup',
      'width=600,height=700',
    );

    if (!popup) {
      console.error('Popup blocked!');
      return;
    }

    // Listen for messages from the popup
    const listener = (event: MessageEvent) => {
      if (event.origin !== window.location.origin) {
        return;
      }

      const { authResponseUrl } = event.data;
      if (authResponseUrl) {
        window.removeEventListener('message', listener);
        popup.close();

        // Remove from long running events
        this.longRunningEvents = this.longRunningEvents.slice(1);

        // Send OAuth response
        this.sendOAuthResponse(
          func,
          authResponseUrl,
          appName,
          agentName,
          userId,
          sessionId,
          onAuthComplete,
        );
      }
    };

    window.addEventListener('message', listener);

    // Handle popup closed manually
    const checkClosed = setInterval(() => {
      if (popup.closed) {
        clearInterval(checkClosed);
        window.removeEventListener('message', listener);
      }
    }, 1000);
  }

  /**
   * Send OAuth response back to the agent
   */
  private async sendOAuthResponse(
    func: LongRunningOperation,
    authResponseUrl: string,
    appName: string,
    agentName: string,
    userId: string,
    sessionId: string,
    onComplete?: () => void,
  ): Promise<void> {
    try {
      const authResponse: AgentRunRequest = {
        appName,
        agentName,
        userId,
        sessionId,
        newMessage: {
          role: 'user',
          parts: [],
        },
        functionCallEventId: this.functionCallEventId,
      };

      // Clone authConfig and update OAuth fields
      const authConfig = structuredClone(func.args.authConfig);
      if (authConfig?.exchangedAuthCredential?.oauth2) {
        authConfig.exchangedAuthCredential.oauth2.authResponseUri =
          authResponseUrl;

        // Send the auth response
        await agentService.runAgent(authResponse);

        if (onComplete) {
          onComplete();
        }
      }
    } catch (error) {
      console.error('Failed to send OAuth response:', error);
    }
  }

  /**
   * Process long running tool operations
   */
  processLongRunningOperations(
    eventData: any,
    appName: string,
    agentName: string,
    userId: string,
    sessionId: string,
    onAuthComplete?: () => void,
  ): void {
    if (
      !eventData.longRunningToolIds ||
      eventData.longRunningToolIds.length === 0
    ) {
      return;
    }

    // Extract async functions from the event content parts
    const asyncFunctions: LongRunningOperation[] = [];
    for (const part of eventData.content.parts) {
      if (
        part.functionCall &&
        eventData.longRunningToolIds.includes(part.functionCall.id)
      ) {
        asyncFunctions.push(part.functionCall);
      }
    }

    // Store long running events
    this.longRunningEvents.push(...asyncFunctions);

    // Get the first function
    const func = asyncFunctions[0];

    // Debug: log current longRunningEvents length for state tracking
    if (process.env.NODE_ENV === 'development') {
      console.log('Long running events count:', this.longRunningEvents.length);
    }

    // Store the event ID
    this.functionCallEventId = eventData.id || '';

    // Handle OAuth flow if present
    if (func?.args?.authConfig?.exchangedAuthCredential?.oauth2) {
      this.handleOAuthFlow(
        func,
        appName,
        agentName,
        userId,
        sessionId,
        onAuthComplete,
      );
    }
  }

  /**
   * Get current long running events count
   */
  getLongRunningCount(): number {
    return this.longRunningEvents.length;
  }

  /**
   * Clear all long running events
   */
  clearLongRunningEvents(): void {
    this.longRunningEvents = [];
    this.functionCallEventId = '';
  }
}

// Export a singleton instance
export const oauthHandler = new OAuthHandler();
