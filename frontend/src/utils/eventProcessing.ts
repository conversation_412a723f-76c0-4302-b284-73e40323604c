import { ChatMessage } from '../types/Agent';
import { processThoughtText, formatBase64Data } from './chatUtils';
import { oauthHandler } from './oauthHandler';

// Re-export utilities that are commonly used
export { processThoughtText, formatBase64Data };

/**
 * Utility functions for processing session events and agent events
 */

/**
 * Process session events into chat messages
 * Used for loading existing session history
 */
export const processSessionEvents = (events: any[]): ChatMessage[] => {
  const existingMessages: ChatMessage[] = [];

  events.forEach((event: any) => {
    // The events have content directly, not nested under event.data
    if (event.content && event.content.parts) {
      event.content.parts.forEach((part: any) => {
        const message = createMessageFromPart(part, event);
        if (message) {
          existingMessages.push(message);
        }
      });
    }

    // Handle agent transfer events (these might be at event level, not part level)
    if (event.actions?.transferToAgent) {
      const transferMessage = createTransferMessage(event);
      existingMessages.push(transferMessage);
    }
  });

  return existingMessages;
};

/**
 * Create a chat message from a content part
 */
export const createMessageFromPart = (
  part: any,
  event: any,
): ChatMessage | null => {
  if (part.functionCall) {
    return createToolCallMessage(part, event);
  } else if (part.functionResponse) {
    return createToolResponseMessage(part, event);
  } else if (part.text) {
    return createTextMessage(part, event);
  } else if (part.inlineData) {
    return createInlineDataMessage(part, event);
  } else if (part.executableCode) {
    return createCodeMessage(part, event);
  } else if (part.codeExecutionResult) {
    return createCodeResultMessage(part, event);
  }

  return null;
};

/**
 * Create tool call message
 */
export const createToolCallMessage = (part: any, event: any): ChatMessage => {
  return {
    id: `tool-call-${event.invocationId || Date.now()}-${Math.random()}`,
    type: 'event',
    content: `Calling ${part.functionCall.name}(${JSON.stringify(part.functionCall.args || {})})`,
    timestamp: event.timestamp || Date.now(),
    eventType: 'tool_call',
    metadata: {
      invocationId: event.invocationId,
      author: event.author,
      functionCall: part.functionCall,
    },
  };
};

/**
 * Create tool response message
 */
export const createToolResponseMessage = (
  part: any,
  event: any,
): ChatMessage => {
  return {
    id: `tool-response-${event.invocationId || Date.now()}-${Math.random()}`,
    type: 'event',
    content: `${part.functionResponse.name} completed`,
    timestamp: event.timestamp || Date.now(),
    eventType: 'tool_response',
    metadata: {
      invocationId: event.invocationId,
      author: event.author,
      functionResponse: part.functionResponse,
    },
  };
};

/**
 * Create text message (user or AI) - handles both streaming and non-streaming
 */
export const createTextMessage = (part: any, event: any): ChatMessage => {
  const messageType = event.author === 'user' ? 'user' : 'ai';
  const isThought = part.thought === true;
  const isPartial = event.partial === true;

  // Generate appropriate ID based on context
  let id: string;
  if (isPartial && event.invocationId) {
    id = `ai-streaming-${event.invocationId}-${event.author}-${isThought ? 'thought' : 'text'}-${event.timestamp || Date.now()}`;
  } else if (!isPartial && event.invocationId) {
    id = `ai-final-${event.invocationId}-${event.author}-${isThought ? 'thought' : 'text'}-${event.timestamp || Date.now()}`;
  } else {
    id = `${event.timestamp || Date.now()}-${Math.random()}`;
  }

  return {
    id,
    type: isThought ? 'event' : messageType,
    content: isThought ? processThoughtText(part.text) : part.text,
    timestamp: event.timestamp || Date.now(),
    eventType: isThought ? 'thought' : undefined,
    metadata: {
      invocationId: event.invocationId,
      author: event.author,
      isThought,
      partial: isPartial,
      renderedContent:
        event.groundingMetadata?.searchEntryPoint?.renderedContent,
    },
  };
};

/**
 * Create inline data message (files, images)
 */
export const createInlineDataMessage = (part: any, event: any): ChatMessage => {
  return {
    id: `inline-data-${event.invocationId || Date.now()}-${Math.random()}`,
    type: 'ai',
    content: part.inlineData.displayName || 'File attachment',
    timestamp: event.timestamp || Date.now(),
    eventType: 'inline_data',
    metadata: {
      invocationId: event.invocationId,
      author: event.author,
      inlineData: {
        displayName: part.inlineData.displayName,
        data: formatBase64Data(part.inlineData.data, part.inlineData.mimeType),
        mimeType: part.inlineData.mimeType,
      },
    },
  };
};

/**
 * Create executable code message
 */
export const createCodeMessage = (part: any, event: any): ChatMessage => {
  return {
    id: `code-${event.invocationId || Date.now()}-${Math.random()}`,
    type: 'event',
    content: `\`\`\`${part.executableCode.language || 'code'}\n${part.executableCode.code}\n\`\`\``,
    timestamp: event.timestamp || Date.now(),
    eventType: 'executable_code',
    metadata: {
      invocationId: event.invocationId,
      author: event.author,
      executableCode: part.executableCode,
    },
  };
};

/**
 * Create code execution result message
 */
export const createCodeResultMessage = (part: any, event: any): ChatMessage => {
  return {
    id: `code-result-${event.invocationId || Date.now()}-${Math.random()}`,
    type: 'event',
    content: `Code execution result: ${part.codeExecutionResult.output || 'Executed successfully'}`,
    timestamp: event.timestamp || Date.now(),
    eventType: 'code_execution_result',
    metadata: {
      invocationId: event.invocationId,
      author: event.author,
      codeExecutionResult: part.codeExecutionResult,
    },
  };
};

/**
 * Create agent transfer message
 */
export const createTransferMessage = (event: any): ChatMessage => {
  return {
    id: `transfer-${event.invocationId || Date.now()}-${Math.random()}`,
    type: 'event',
    content: `Transferring to agent: ${event.actions.transferToAgent}`,
    timestamp: event.timestamp || Date.now(),
    eventType: 'agent_transfer',
    metadata: {
      invocationId: event.invocationId,
      author: event.author,
      transferToAgent: event.actions.transferToAgent,
    },
  };
};

/**
 * Create error message
 */
export const createErrorMessage = (
  errorText: string,
  invocationId?: string,
): ChatMessage => {
  return {
    id: `error-${Date.now()}-${Math.random()}`,
    type: 'event',
    content: `❌ Error: ${errorText}`,
    timestamp: Date.now(),
    eventType: 'error',
    metadata: {
      invocationId,
      error: errorText,
    },
  };
};

/**
 * Create long running operation message
 */
export const createLongRunningMessage = (
  invocationId: string,
  author: string,
  toolIds: string[],
): ChatMessage => {
  return {
    id: `long-running-${Date.now()}-${Math.random()}`,
    type: 'event',
    content: `Long running operation in progress...`,
    timestamp: Date.now(),
    eventType: 'long_running_operation',
    metadata: {
      invocationId,
      author,
      longRunningToolIds: toolIds,
    },
  };
};

/**
 * Create artifact update message
 */
export const createArtifactMessage = (
  artifactId: string,
  versionId: any,
  invocationId: string,
  author: string,
): ChatMessage => {
  return {
    id: `artifact-${Date.now()}-${Math.random()}`,
    type: 'event',
    content: `Artifact updated: ${artifactId}`,
    timestamp: Date.now(),
    eventType: 'artifact_update',
    metadata: {
      invocationId,
      author,
      artifactId,
      versionId,
    },
  };
};
/**
 * Process agent event data including content parts and special actions
 * This centralizes all event processing logic in one function
 */
export const processAgentEvent = (
  eventData: any,
  onMessagesUpdate: (updater: (prev: ChatMessage[]) => ChatMessage[]) => void,
  onOverlayUpdate: (content: ChatMessage | null) => void,
  onProcessingComplete?: () => void,
  sessionContext?: {
    appName: string;
    agentName: string;
    userId: string;
    sessionId: string;
  },
): void => {
  const invocationId = eventData.invocationId;
  const author = eventData.author;

  // Process content parts if they exist
  const parts = eventData.content?.parts || [];
  parts.forEach((part: any) => {
    if (part.text) {
      handleTextMessage(
        part,
        eventData,
        onMessagesUpdate,
        onOverlayUpdate,
        onProcessingComplete,
      );
    } else {
      // Handle non-text messages (function calls, responses, inline data, etc.)
      handleNonTextMessage(part, eventData, onMessagesUpdate, onOverlayUpdate);
    }
  });

  // Handle long running tool operations (OAuth, async functions)
  if (eventData.longRunningToolIds && eventData.longRunningToolIds.length > 0) {
    const longRunningMessage = createLongRunningMessage(
      invocationId,
      author,
      eventData.longRunningToolIds,
    );
    onMessagesUpdate(prev => [...prev, longRunningMessage]);
    onOverlayUpdate(longRunningMessage); // Show as overlay

    // Handle OAuth if session context is provided
    if (sessionContext) {
      oauthHandler.processLongRunningOperations(
        eventData,
        sessionContext.appName,
        sessionContext.agentName,
        sessionContext.userId,
        sessionContext.sessionId,
      );
    }
  }

  // Handle artifact deltas
  if (eventData.actions?.artifactDelta) {
    Object.entries(eventData.actions.artifactDelta).forEach(
      ([artifactId, versionId]) => {
        const artifactMessage = createArtifactMessage(
          artifactId,
          versionId,
          invocationId,
          author,
        );
        onMessagesUpdate(prev => [...prev, artifactMessage]);
      },
    );
  }

  // Handle agent transfer events
  if (eventData.actions?.transferToAgent) {
    const transferMessage = createTransferMessage({
      invocationId,
      author,
      timestamp: Date.now(),
      actions: { transferToAgent: eventData.actions.transferToAgent },
    });
    onMessagesUpdate(prev => [...prev, transferMessage]);
    onOverlayUpdate(transferMessage); // Show as overlay
  }
};

/**
 * Handle text messages with streaming logic
 */
export const handleTextMessage = (
  part: any,
  event: any,
  onMessagesUpdate: (updater: (prev: ChatMessage[]) => ChatMessage[]) => void,
  onOverlayUpdate: (content: ChatMessage | null) => void,
  onProcessingComplete?: () => void,
): void => {
  const isPartial = event.partial === true;
  const invocationId = event.invocationId;
  const author = event.author;
  const isThought = part.thought === true;

  if (isPartial && invocationId) {
    // This is a partial message - update or create streaming message
    onMessagesUpdate(prev => {
      // Find existing streaming message with same invocationId AND author
      const existingIndex = prev.findIndex(
        msg =>
          msg.type === (isThought ? 'event' : 'ai') &&
          msg.metadata?.invocationId === invocationId &&
          msg.metadata?.author === author &&
          msg.metadata?.partial === true &&
          msg.metadata?.isThought === isThought,
      );

      if (existingIndex !== -1) {
        // Update existing streaming message by appending new text
        const updatedMessages = [...prev];
        const existingContent = updatedMessages[existingIndex].content;
        updatedMessages[existingIndex] = {
          ...updatedMessages[existingIndex],
          content: existingContent + part.text,
          timestamp: Date.now(),
          metadata: {
            ...updatedMessages[existingIndex].metadata,
            partial: true,
          },
        };

        // Show overlay for thoughts or streaming content
        if (isThought || author !== 'user') {
          onOverlayUpdate(updatedMessages[existingIndex]);
        }

        return updatedMessages;
      } else {
        // Add new streaming message
        const streamingMessage = createTextMessage(part, event);

        // Show overlay for thoughts or streaming content
        if (isThought || author !== 'user') {
          onOverlayUpdate(streamingMessage);
        }

        return [...prev, streamingMessage];
      }
    });
  } else {
    // This is a final complete message
    if (isPartial === false && invocationId) {
      // Replace any partial version with final version
      onMessagesUpdate(prev => {
        const withoutStreaming = prev.filter(
          msg =>
            !(
              msg.type === (isThought ? 'event' : 'ai') &&
              msg.metadata?.invocationId === invocationId &&
              msg.metadata?.author === author &&
              msg.metadata?.partial === true &&
              msg.metadata?.isThought === isThought
            ),
        );
        const finalMessage = createTextMessage(part, event);
        onOverlayUpdate(null);
        if (author !== 'user' && !isThought && onProcessingComplete) {
          onProcessingComplete();
        }

        return [...withoutStreaming, finalMessage];
      });
    } else {
      const textMessage = createTextMessage(part, event);
      onMessagesUpdate(prev => [...prev, textMessage]);
      onOverlayUpdate(null);
      if (author !== 'user' && !isThought && onProcessingComplete) {
        onProcessingComplete();
      }
    }
  }
};

/**
 * Handle non-text content parts (function calls, responses, etc.)
 */
export const handleNonTextMessage = (
  part: any,
  event: any,
  onMessagesUpdate: (updater: (prev: ChatMessage[]) => ChatMessage[]) => void,
  onOverlayUpdate: (content: ChatMessage | null) => void,
): void => {
  const message = createMessageFromPart(part, event);

  if (message) {
    onMessagesUpdate(prev => [...prev, message]);

    // Show overlay for events
    if (message.type === 'event') {
      onOverlayUpdate(message);
    }
  }
};
