export interface Invocation {
  invocationId: string;
  userContent: Content;
  finalResponse?: Content;
  intermediateData?: IntermediateData;
  creationTimestamp: number;
}

export interface Content {
  parts?: any[];
  role?: string | null;
}

export interface IntermediateData {
  toolUses: any[];
  intermediateResponses: any[];
}

export interface Session {
  id?: string;
  appName?: string;
  userId?: string;
  state?: any;
  events?: any;
  messages?: any;
}

export interface AgentRunRequest {
  appName: string;
  agentName: string;
  userId: string;
  sessionId: string;
  newMessage: Content;
  functionCallEventId?: string;
  streaming?: boolean;
  stateDelta?: any;
}

export interface LiveRequest {
  content?: any;
  blob?: any;
  close?: boolean;
  model_config?: any;
}

export type EventType =
  | 'tool_call'
  | 'tool_response'
  | 'agent_transfer'
  | 'inline_data'
  | 'executable_code'
  | 'code_execution_result'
  | 'thought'
  | 'long_running_operation'
  | 'artifact_update'
  | 'evaluation'
  | 'error';

export interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'system' | 'event';
  content: string;
  timestamp: number;
  eventType?: EventType;
  metadata?: Record<string, any>;
}

export interface ChatState {
  isProcessing: boolean;
  isConnected: boolean;
  currentEventId?: string;
  error?: string;
}

export interface AgentEvent {
  id: string;
  type: string;
  data: any;
  timestamp: number;
}

export interface AgentResponse {
  events: AgentEvent[];
  finalResponse?: string;
}
