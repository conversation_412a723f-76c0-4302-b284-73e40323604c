# Stage 1: Build the React application
FROM node:22-alpine AS build
ENV HUSKY=0
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build the application with error checking
RUN pnpm run build && \
    ls -la dist/ && \
    test -d dist && \
    test "$(ls -A dist)" || (echo "Build failed - dist directory is empty" && exit 1)

# Stage 2: Serve the built application with Nginx
FROM nginx:alpine
# Default config for local development
ENV API_URL=http://localhost:2000/api
RUN apk add --no-cache curl && rm /etc/nginx/conf.d/default.conf
COPY nginx/nginx.conf /etc/nginx/conf.d
COPY nginx/config-env.sh /docker-entrypoint.d/40-config-env.sh
RUN chmod +x /docker-entrypoint.d/40-config-env.sh
COPY --from=build /app/dist /usr/share/nginx/html


EXPOSE 2000
CMD ["nginx", "-g", "daemon off;"]
